package com.vexleyofficial.ravyn.launcher.version;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;
import java.util.Map;

/**
 * Data classes for Minecraft version manifest
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class VersionManifest {
    
    public String id;
    public String type;
    public String time;
    public String releaseTime;
    public String minecraftArguments;
    public String mainClass;
    public int minimumLauncherVersion;
    public String assets;
    public Downloads downloads;
    public List<Library> libraries;
    public AssetIndex assetIndex;
    public Arguments arguments;
    public JavaVersion javaVersion;
    
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Downloads {
        public Download client;
        public Download server;
        public Download client_mappings;
        public Download server_mappings;
    }
    
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Download {
        public String sha1;
        public int size;
        public String url;
        public String path;
    }
    
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Library {
        public String name;
        public Downloads downloads;
        public List<Rule> rules;
        public Map<String, String> natives;
        public Extract extract;
        
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Downloads {
            public Download artifact;
            public Map<String, Download> classifiers;
        }
        
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Rule {
            public String action;
            public OS os;
            
            @JsonIgnoreProperties(ignoreUnknown = true)
            public static class OS {
                public String name;
                public String version;
                public String arch;
            }
        }
        
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Extract {
            public List<String> exclude;
        }
    }
    
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AssetIndex {
        public String id;
        public String sha1;
        public int size;
        public int totalSize;
        public String url;
    }
    
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Arguments {
        public List<Object> game;
        public List<Object> jvm;
    }
    
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class JavaVersion {
        public String component;
        public int majorVersion;
    }
}
