package com.vexleyofficial.ravyn.client.command.commands;

import com.vexleyofficial.ravyn.client.command.Command;

/**
 * Gamemode command - Changes gamemode (creative servers only)
 */
public class GamemodeCommand extends Command {
    
    public GamemodeCommand() {
        super("gamemode", "Changes gamemode", ".gamemode <0/1/2/3>", "gm");
    }
    
    @Override
    public void execute(String[] args) throws Exception {
        if (args.length != 1) {
            sendUsage();
            return;
        }
        
        if (mc.player == null || mc.getNetworkHandler() == null) {
            sendError("You must be in-game to use this command.");
            return;
        }
        
        String mode = args[0];
        String gamemode = switch (mode) {
            case "0", "s", "survival" -> "survival";
            case "1", "c", "creative" -> "creative";
            case "2", "a", "adventure" -> "adventure";
            case "3", "sp", "spectator" -> "spectator";
            default -> null;
        };
        
        if (gamemode == null) {
            sendError("Invalid gamemode. Use 0-3 or survival/creative/adventure/spectator.");
            return;
        }
        
        // Send gamemode command to server
        mc.player.networkHandler.sendChatCommand("gamemode " + gamemode);
        sendSuccess("Attempted to change gamemode to §f" + gamemode + "§a.");
    }
}
