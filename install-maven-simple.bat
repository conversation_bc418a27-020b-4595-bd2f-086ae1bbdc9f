@echo off
echo ========================================
echo    Simple Maven Installation
echo ========================================
echo.

REM Create local tools directory
set TOOLS_DIR=%USERPROFILE%\tools
set MAVEN_DIR=%TOOLS_DIR%\maven

echo [1/4] Creating tools directory...
if not exist "%TOOLS_DIR%" mkdir "%TOOLS_DIR%"
echo Created: %TOOLS_DIR%

echo.
echo [2/4] Downloading Maven...
echo This will open your browser to download Maven manually.
echo.
echo Please:
echo 1. Download apache-maven-3.9.5-bin.zip from the opened page
echo 2. Extract it to: %TOOLS_DIR%
echo 3. Rename the extracted folder to: maven
echo 4. Come back here and press any key
echo.

start https://archive.apache.org/dist/maven/maven-3/3.9.5/binaries/apache-maven-3.9.5-bin.zip

pause

echo.
echo [3/4] Checking if <PERSON><PERSON> was extracted...
if not exist "%MAVEN_DIR%\bin\mvn.cmd" (
    echo ERROR: Maven not found at %MAVEN_DIR%
    echo.
    echo Please make sure you:
    echo 1. Downloaded apache-maven-3.9.5-bin.zip
    echo 2. Extracted it to %TOOLS_DIR%
    echo 3. Renamed the folder to 'maven'
    echo.
    echo Expected structure:
    echo %MAVEN_DIR%\bin\mvn.cmd
    echo.
    pause
    exit /b 1
)

echo Maven found at: %MAVEN_DIR%

echo.
echo [4/4] Setting up environment for current session...

REM Set environment variables for current session
set MAVEN_HOME=%MAVEN_DIR%
set PATH=%PATH%;%MAVEN_DIR%\bin

echo MAVEN_HOME set to: %MAVEN_HOME%
echo Added to PATH: %MAVEN_DIR%\bin

echo.
echo Testing Maven installation...
call mvn -version
if %errorlevel% neq 0 (
    echo ERROR: Maven test failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo    MAVEN INSTALLATION COMPLETE!
echo ========================================
echo.
echo Maven is now available for this session.
echo.
echo To make it permanent, add these to your system environment variables:
echo MAVEN_HOME=%MAVEN_DIR%
echo PATH=%PATH%
echo.
echo Or run this command as Administrator:
echo setx MAVEN_HOME "%MAVEN_DIR%" /M
echo setx PATH "%PATH%;%MAVEN_DIR%\bin" /M
echo.
echo Now you can run the Ravyn build:
echo .\build.bat
echo.
pause
