package com.vexleyofficial.ravyn.client.setting;

import com.vexleyofficial.ravyn.client.util.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * Manages all client settings
 */
public class SettingManager {
    
    private static final Logger logger = Logger.getLogger(SettingManager.class);
    
    private final List<Setting<?>> settings = new ArrayList<>();
    
    public SettingManager() {
        logger.info("Setting manager initialized");
    }
    
    public void addSetting(Setting<?> setting) {
        settings.add(setting);
    }
    
    public void removeSetting(Setting<?> setting) {
        settings.remove(setting);
    }
    
    public List<Setting<?>> getSettings() {
        return new ArrayList<>(settings);
    }
    
    public Setting<?> getSetting(String name) {
        return settings.stream()
            .filter(setting -> setting.getName().equalsIgnoreCase(name))
            .findFirst()
            .orElse(null);
    }
    
    public void resetAll() {
        settings.forEach(Setting::reset);
        logger.info("Reset all settings to defaults");
    }
}
