package com.vexleyofficial.ravyn.client.mixins;

import com.vexleyofficial.ravyn.client.RavynClient;
import net.minecraft.client.Keyboard;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

/**
 * Mixin for Keyboard to handle key presses
 */
@Mixin(Keyboard.class)
public class KeyboardMixin {
    
    @Inject(method = "onKey", at = @At("HEAD"))
    private void onKeyPress(long window, int key, int scancode, int action, int modifiers, CallbackInfo ci) {
        if (action == 1 && RavynClient.getInstance() != null) { // Key press
            RavynClient.getInstance().getModuleManager().onKeyPress(key);
        }
    }
}
