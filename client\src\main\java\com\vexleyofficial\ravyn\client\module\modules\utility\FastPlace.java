package com.vexleyofficial.ravyn.client.module.modules.utility;

import com.vexleyofficial.ravyn.client.module.Module;
import com.vexleyofficial.ravyn.client.setting.NumberSetting;

/**
 * FastPlace module - Places blocks faster
 */
public class FastPlace extends Module {
    
    private final NumberSetting delay = new NumberSetting("Delay", 0, 0, 4, 1);
    
    public FastPlace() {
        super("FastPlace", "Places blocks faster", Category.UTILITY);
        addSetting(delay);
    }
    
    public int getDelay() {
        return isEnabled() ? delay.getValueInt() : 4;
    }
}
