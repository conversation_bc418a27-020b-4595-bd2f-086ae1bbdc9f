package com.vexleyofficial.ravyn.client.command;

import com.vexleyofficial.ravyn.client.RavynClient;
import com.vexleyofficial.ravyn.client.command.commands.*;
import com.vexleyofficial.ravyn.client.util.Logger;
import net.minecraft.client.MinecraftClient;
import net.minecraft.text.Text;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Manages client commands
 */
public class CommandManager {
    
    private static final Logger logger = Logger.getLogger(CommandManager.class);
    private static final String PREFIX = ".";
    
    private final List<Command> commands = new ArrayList<>();
    private final MinecraftClient mc = MinecraftClient.getInstance();
    
    public CommandManager() {
        logger.info("Initializing command system...");
        
        // Register commands
        addCommand(new HelpCommand());
        addCommand(new ToggleCommand());
        addCommand(new BindCommand());
        addCommand(new ConfigCommand());
        addCommand(new FriendCommand());
        addCommand(new EnemyCommand());
        addCommand(new SayCommand());
        addCommand(new VClipCommand());
        addCommand(new HClipCommand());
        addCommand(new GamemodeCommand());
        
        logger.info("Loaded {} commands", commands.size());
    }
    
    private void addCommand(Command command) {
        commands.add(command);
        logger.debug("Registered command: {}", command.getName());
    }
    
    /**
     * Process a chat message for commands
     */
    public boolean processMessage(String message) {
        if (!message.startsWith(PREFIX)) {
            return false;
        }
        
        // Remove prefix and split arguments
        String commandLine = message.substring(PREFIX.length());
        String[] args = commandLine.split("\\s+");
        
        if (args.length == 0) {
            return false;
        }
        
        String commandName = args[0].toLowerCase();
        String[] commandArgs = Arrays.copyOfRange(args, 1, args.length);
        
        // Find and execute command
        for (Command command : commands) {
            if (command.getName().equalsIgnoreCase(commandName) || 
                command.getAliases().contains(commandName)) {
                
                try {
                    command.execute(commandArgs);
                    return true;
                } catch (Exception e) {
                    sendMessage("§cError executing command: " + e.getMessage());
                    logger.error("Error executing command: " + commandName, e);
                    return true;
                }
            }
        }
        
        sendMessage("§cUnknown command. Type §f" + PREFIX + "help §cfor a list of commands.");
        return true;
    }
    
    /**
     * Send a message to the player
     */
    public void sendMessage(String message) {
        if (mc.player != null) {
            mc.player.sendMessage(Text.literal("§8[§cRavyn§8] §r" + message), false);
        }
    }
    
    /**
     * Get all commands
     */
    public List<Command> getCommands() {
        return new ArrayList<>(commands);
    }
    
    /**
     * Get command by name
     */
    public Command getCommand(String name) {
        return commands.stream()
            .filter(cmd -> cmd.getName().equalsIgnoreCase(name) || 
                          cmd.getAliases().contains(name.toLowerCase()))
            .findFirst()
            .orElse(null);
    }
    
    public static String getPrefix() {
        return PREFIX;
    }
}
