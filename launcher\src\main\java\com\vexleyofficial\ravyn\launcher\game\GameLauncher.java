package com.vexleyofficial.ravyn.launcher.game;

import com.vexleyofficial.ravyn.launcher.util.LauncherConfig;
import com.vexleyofficial.ravyn.launcher.util.Logger;
import com.vexleyofficial.ravyn.launcher.version.VersionManifest;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;

/**
 * <PERSON>les launching Minecraft with proper arguments and classpath
 */
public class GameLauncher {
    
    private static final Logger logger = Logger.getLogger(GameLauncher.class);
    
    /**
     * Launch Minecraft with the specified configuration
     */
    public Process launchGame(LaunchConfig config) throws IOException {
        logger.info("Launching Minecraft {} for user {}", config.version, config.account.getUsername());
        
        List<String> command = buildLaunchCommand(config);
        
        if (logger != null) {
            logger.debug("Launch command: {}", String.join(" ", command));
        }
        
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.directory(new File(LauncherConfig.getMinecraftDir()));
        
        // Set environment variables
        processBuilder.environment().put("APPDATA", System.getProperty("user.home"));
        
        return processBuilder.start();
    }
    
    private List<String> buildLaunchCommand(LaunchConfig config) {
        List<String> command = new ArrayList<>();
        
        // Java executable
        String javaPath = config.javaPath;
        if (javaPath == null || javaPath.isEmpty()) {
            javaPath = System.getProperty("java.home") + File.separator + "bin" + File.separator + "java";
        }
        command.add(javaPath);
        
        // JVM arguments
        addJvmArguments(command, config);
        
        // Classpath
        String classpath = buildClasspath(config);
        command.add("-cp");
        command.add(classpath);
        
        // Main class
        String mainClass = config.manifest.mainClass;
        if (config.useRavynClient) {
            // Use Ravyn client main class if available
            mainClass = "com.vexleyofficial.ravyn.client.RavynClient";
        }
        command.add(mainClass);
        
        // Game arguments
        addGameArguments(command, config);
        
        return command;
    }
    
    private void addJvmArguments(List<String> command, LaunchConfig config) {
        // Custom JVM arguments from config
        if (config.jvmArgs != null && !config.jvmArgs.isEmpty()) {
            String[] args = config.jvmArgs.split("\\s+");
            for (String arg : args) {
                if (!arg.trim().isEmpty()) {
                    command.add(arg.trim());
                }
            }
        }
        
        // Default JVM arguments
        command.add("-Djava.library.path=" + getNativesPath(config));
        command.add("-Dminecraft.launcher.brand=ravyn");
        command.add("-Dminecraft.launcher.version=" + com.vexleyofficial.ravyn.launcher.RavynLauncher.getVersion());
        
        // OS-specific arguments
        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.contains("mac")) {
            command.add("-XstartOnFirstThread");
        }
        
        // Version-specific arguments
        if (config.manifest.arguments != null && config.manifest.arguments.jvm != null) {
            for (Object arg : config.manifest.arguments.jvm) {
                if (arg instanceof String) {
                    command.add(resolveArgument((String) arg, config));
                }
                // TODO: Handle conditional arguments
            }
        }
    }
    
    private void addGameArguments(List<String> command, LaunchConfig config) {
        // Legacy arguments (pre-1.13)
        if (config.manifest.minecraftArguments != null) {
            String[] args = config.manifest.minecraftArguments.split("\\s+");
            for (String arg : args) {
                command.add(resolveArgument(arg, config));
            }
        }
        
        // Modern arguments (1.13+)
        if (config.manifest.arguments != null && config.manifest.arguments.game != null) {
            for (Object arg : config.manifest.arguments.game) {
                if (arg instanceof String) {
                    command.add(resolveArgument((String) arg, config));
                }
                // TODO: Handle conditional arguments
            }
        }
        
        // Additional arguments
        if (config.additionalArgs != null && !config.additionalArgs.isEmpty()) {
            String[] args = config.additionalArgs.split("\\s+");
            for (String arg : args) {
                if (!arg.trim().isEmpty()) {
                    command.add(arg.trim());
                }
            }
        }
    }
    
    private String buildClasspath(LaunchConfig config) {
        StringJoiner classpath = new StringJoiner(File.pathSeparator);
        
        // Add client JAR
        Path clientJar = Paths.get(LauncherConfig.getVersionsDir(), config.version, config.version + ".jar");
        classpath.add(clientJar.toString());
        
        // Add Ravyn client JAR if enabled
        if (config.useRavynClient) {
            Path ravynJar = Paths.get(LauncherConfig.getConfigDir(), "client", "ravyn-client.jar");
            if (ravynJar.toFile().exists()) {
                classpath.add(ravynJar.toString());
            }
        }
        
        // Add libraries
        for (VersionManifest.Library library : config.manifest.libraries) {
            if (shouldIncludeLibrary(library)) {
                if (library.downloads != null && library.downloads.artifact != null) {
                    Path libPath = Paths.get(LauncherConfig.getLibrariesDir(), library.downloads.artifact.path);
                    classpath.add(libPath.toString());
                }
            }
        }
        
        return classpath.toString();
    }
    
    private boolean shouldIncludeLibrary(VersionManifest.Library library) {
        if (library.rules == null || library.rules.isEmpty()) {
            return true;
        }
        
        boolean allowed = false;
        for (VersionManifest.Library.Rule rule : library.rules) {
            if ("allow".equals(rule.action)) {
                if (rule.os == null || matchesOS(rule.os)) {
                    allowed = true;
                }
            } else if ("disallow".equals(rule.action)) {
                if (rule.os == null || matchesOS(rule.os)) {
                    allowed = false;
                }
            }
        }
        
        return allowed;
    }
    
    private boolean matchesOS(VersionManifest.Library.Rule.OS osRule) {
        String osName = System.getProperty("os.name").toLowerCase();
        String osArch = System.getProperty("os.arch").toLowerCase();
        
        if (osRule.name != null) {
            String ruleName = osRule.name.toLowerCase();
            if (ruleName.equals("windows") && !osName.contains("windows")) return false;
            if (ruleName.equals("linux") && !osName.contains("linux")) return false;
            if (ruleName.equals("osx") && !osName.contains("mac")) return false;
        }
        
        if (osRule.arch != null) {
            if (!osArch.contains(osRule.arch.toLowerCase())) return false;
        }
        
        return true;
    }
    
    private String getNativesPath(LaunchConfig config) {
        return Paths.get(LauncherConfig.getVersionsDir(), config.version, "natives").toString();
    }
    
    private String resolveArgument(String arg, LaunchConfig config) {
        return arg
            .replace("${auth_player_name}", config.account.getUsername())
            .replace("${version_name}", config.version)
            .replace("${game_directory}", LauncherConfig.getMinecraftDir())
            .replace("${assets_root}", LauncherConfig.getAssetsDir())
            .replace("${assets_index_name}", config.manifest.assets)
            .replace("${auth_uuid}", config.account.getUuid())
            .replace("${auth_access_token}", config.account.getAccessToken() != null ? config.account.getAccessToken() : "null")
            .replace("${user_type}", config.account.isOffline() ? "legacy" : "mojang")
            .replace("${version_type}", config.manifest.type)
            .replace("${resolution_width}", String.valueOf(config.windowWidth))
            .replace("${resolution_height}", String.valueOf(config.windowHeight))
            .replace("${launcher_name}", "ravyn")
            .replace("${launcher_version}", com.vexleyofficial.ravyn.launcher.RavynLauncher.getVersion())
            .replace("${classpath_separator}", File.pathSeparator);
    }
    
    /**
     * Launch configuration
     */
    public static class LaunchConfig {
        public String version;
        public VersionManifest manifest;
        public LauncherConfig.Account account;
        public boolean useRavynClient;
        public String javaPath;
        public String jvmArgs;
        public String additionalArgs;
        public int windowWidth;
        public int windowHeight;
        public boolean fullscreen;
        
        public LaunchConfig(String version, VersionManifest manifest, LauncherConfig.Account account) {
            this.version = version;
            this.manifest = manifest;
            this.account = account;
            
            // Load defaults from config
            LauncherConfig config = LauncherConfig.getInstance();
            this.useRavynClient = config.isUseRavynClient();
            this.javaPath = config.getJavaPath();
            this.jvmArgs = config.getJvmArgs();
            this.windowWidth = config.getWindowWidth();
            this.windowHeight = config.getWindowHeight();
            this.fullscreen = config.isFullscreen();
        }
    }
}
