package com.vexleyofficial.ravyn.client.command.commands;

import com.vexleyofficial.ravyn.client.RavynClient;
import com.vexleyofficial.ravyn.client.command.Command;
import com.vexleyofficial.ravyn.client.module.Module;

/**
 * Toggle command - Toggles modules on/off
 */
public class ToggleCommand extends Command {
    
    public ToggleCommand() {
        super("toggle", "Toggles a module on/off", ".toggle <module>", "t");
    }
    
    @Override
    public void execute(String[] args) throws Exception {
        if (args.length != 1) {
            sendUsage();
            return;
        }
        
        String moduleName = args[0];
        Module module = RavynClient.getInstance().getModuleManager().getModule(moduleName);
        
        if (module == null) {
            sendError("Module '" + moduleName + "' not found.");
            return;
        }
        
        module.toggle();
        
        String status = module.isEnabled() ? "§aenabled" : "§cdisabled";
        sendSuccess("Module §f" + module.getName() + " §ais now " + status + "§a.");
    }
}
