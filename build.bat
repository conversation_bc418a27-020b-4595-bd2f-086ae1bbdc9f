@echo off
echo Building Ravyn Launcher and Client...

echo.
echo [1/3] Building Launcher...
cd launcher
call mvn clean package
if %errorlevel% neq 0 (
    echo Failed to build launcher
    pause
    exit /b 1
)
cd ..

echo.
echo [2/3] Building Client...
cd client
call gradlew build
if %errorlevel% neq 0 (
    echo Failed to build client
    pause
    exit /b 1
)
cd ..

echo.
echo [3/3] Building Installer...
cd installer
call mvn clean package
if %errorlevel% neq 0 (
    echo Failed to build installer
    pause
    exit /b 1
)
cd ..

echo.
echo Build completed successfully!
echo.
echo Output files:
echo - Launcher: launcher\target\ravyn-launcher-1.0.0.jar
echo - Client: client\build\libs\ravyn-client-1.0.0.jar
echo - Installer: installer\target\ravyn-installer-1.0.0.jar
echo.
pause
