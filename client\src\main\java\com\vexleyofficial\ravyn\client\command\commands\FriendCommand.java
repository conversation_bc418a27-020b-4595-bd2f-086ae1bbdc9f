package com.vexleyofficial.ravyn.client.command.commands;

import com.vexleyofficial.ravyn.client.RavynClient;
import com.vexleyofficial.ravyn.client.command.Command;
import com.vexleyofficial.ravyn.client.friend.FriendManager;

/**
 * Friend command - Manages friends list
 */
public class FriendCommand extends Command {
    
    public FriendCommand() {
        super("friend", "Manages friends list", ".friend <add/remove/list> [player]", "f");
    }
    
    @Override
    public void execute(String[] args) throws Exception {
        if (args.length == 0) {
            sendUsage();
            return;
        }
        
        FriendManager friendManager = RavynClient.getInstance().getFriendManager();
        String action = args[0].toLowerCase();
        
        switch (action) {
            case "add" -> {
                if (args.length != 2) {
                    sendError("Usage: .friend add <player>");
                    return;
                }
                String playerName = args[1];
                if (friendManager.addFriend(playerName)) {
                    sendSuccess("Added §f" + playerName + " §ato friends list.");
                } else {
                    sendError(playerName + " is already in your friends list.");
                }
            }
            
            case "remove", "del" -> {
                if (args.length != 2) {
                    sendError("Usage: .friend remove <player>");
                    return;
                }
                String playerName = args[1];
                if (friendManager.removeFriend(playerName)) {
                    sendSuccess("Removed §f" + playerName + " §afrom friends list.");
                } else {
                    sendError(playerName + " is not in your friends list.");
                }
            }
            
            case "list" -> {
                var friends = friendManager.getFriends();
                if (friends.isEmpty()) {
                    sendMessage("§7Your friends list is empty.");
                } else {
                    sendMessage("§7Friends (" + friends.size() + "):");
                    for (String friend : friends) {
                        sendMessage("§f- " + friend);
                    }
                }
            }
            
            case "clear" -> {
                friendManager.clearFriends();
                sendSuccess("Cleared friends list.");
            }
            
            default -> sendError("Invalid action. Use add, remove, list, or clear.");
        }
    }
}
