package com.vexleyofficial.ravyn.client.event;

import com.vexleyofficial.ravyn.client.util.Logger;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Event system for handling client events
 */
public class EventManager {
    
    private static final Logger logger = Logger.getLogger(EventManager.class);
    
    private final Map<Class<? extends Event>, List<EventListener>> listeners = new ConcurrentHashMap<>();
    
    /**
     * Register an event listener
     */
    public void register(Object listener) {
        Class<?> clazz = listener.getClass();
        
        for (Method method : clazz.getDeclaredMethods()) {
            if (method.isAnnotationPresent(EventHandler.class)) {
                Class<?>[] parameters = method.getParameterTypes();
                
                if (parameters.length == 1 && Event.class.isAssignableFrom(parameters[0])) {
                    @SuppressWarnings("unchecked")
                    Class<? extends Event> eventType = (Class<? extends Event>) parameters[0];
                    
                    EventHandler annotation = method.getAnnotation(EventHandler.class);
                    EventListener eventListener = new EventListener(listener, method, annotation.priority());
                    
                    listeners.computeIfAbsent(eventType, k -> new ArrayList<>()).add(eventListener);
                    
                    logger.debug("Registered event listener: {} for event {}", 
                        method.getName(), eventType.getSimpleName());
                }
            }
        }
        
        // Sort listeners by priority
        for (List<EventListener> listenerList : listeners.values()) {
            listenerList.sort((a, b) -> Integer.compare(b.priority.ordinal(), a.priority.ordinal()));
        }
    }
    
    /**
     * Unregister an event listener
     */
    public void unregister(Object listener) {
        listeners.values().forEach(list -> 
            list.removeIf(eventListener -> eventListener.instance == listener));
    }
    
    /**
     * Post an event to all registered listeners
     */
    public <T extends Event> T post(T event) {
        List<EventListener> eventListeners = listeners.get(event.getClass());
        
        if (eventListeners != null) {
            for (EventListener listener : eventListeners) {
                try {
                    listener.method.setAccessible(true);
                    listener.method.invoke(listener.instance, event);
                    
                    if (event.isCancelled()) {
                        break;
                    }
                } catch (Exception e) {
                    logger.error("Error invoking event listener", e);
                }
            }
        }
        
        return event;
    }
    
    /**
     * Event listener wrapper
     */
    private static class EventListener {
        final Object instance;
        final Method method;
        final EventPriority priority;
        
        EventListener(Object instance, Method method, EventPriority priority) {
            this.instance = instance;
            this.method = method;
            this.priority = priority;
        }
    }
}
