package com.vexleyofficial.ravyn.client.command.commands;

import com.vexleyofficial.ravyn.client.RavynClient;
import com.vexleyofficial.ravyn.client.command.Command;
import com.vexleyofficial.ravyn.client.module.Module;
import org.lwjgl.glfw.GLFW;

/**
 * Bind command - Sets keybinds for modules
 */
public class BindCommand extends Command {
    
    public BindCommand() {
        super("bind", "Sets a keybind for a module", ".bind <module> <key>", "b");
    }
    
    @Override
    public void execute(String[] args) throws Exception {
        if (args.length != 2) {
            sendUsage();
            return;
        }
        
        String moduleName = args[0];
        String keyName = args[1].toLowerCase();
        
        Module module = RavynClient.getInstance().getModuleManager().getModule(moduleName);
        if (module == null) {
            sendError("Module '" + moduleName + "' not found.");
            return;
        }
        
        int keyCode = getKeyCode(keyName);
        if (keyCode == -1) {
            sendError("Invalid key '" + keyName + "'.");
            return;
        }
        
        module.setKey(keyCode);
        sendSuccess("Module §f" + module.getName() + " §ais now bound to §f" + keyName.toUpperCase() + "§a.");
    }
    
    private int getKeyCode(String keyName) {
        return switch (keyName) {
            case "none", "unbind" -> 0;
            case "a" -> GLFW.GLFW_KEY_A;
            case "b" -> GLFW.GLFW_KEY_B;
            case "c" -> GLFW.GLFW_KEY_C;
            case "d" -> GLFW.GLFW_KEY_D;
            case "e" -> GLFW.GLFW_KEY_E;
            case "f" -> GLFW.GLFW_KEY_F;
            case "g" -> GLFW.GLFW_KEY_G;
            case "h" -> GLFW.GLFW_KEY_H;
            case "i" -> GLFW.GLFW_KEY_I;
            case "j" -> GLFW.GLFW_KEY_J;
            case "k" -> GLFW.GLFW_KEY_K;
            case "l" -> GLFW.GLFW_KEY_L;
            case "m" -> GLFW.GLFW_KEY_M;
            case "n" -> GLFW.GLFW_KEY_N;
            case "o" -> GLFW.GLFW_KEY_O;
            case "p" -> GLFW.GLFW_KEY_P;
            case "q" -> GLFW.GLFW_KEY_Q;
            case "r" -> GLFW.GLFW_KEY_R;
            case "s" -> GLFW.GLFW_KEY_S;
            case "t" -> GLFW.GLFW_KEY_T;
            case "u" -> GLFW.GLFW_KEY_U;
            case "v" -> GLFW.GLFW_KEY_V;
            case "w" -> GLFW.GLFW_KEY_W;
            case "x" -> GLFW.GLFW_KEY_X;
            case "y" -> GLFW.GLFW_KEY_Y;
            case "z" -> GLFW.GLFW_KEY_Z;
            case "f1" -> GLFW.GLFW_KEY_F1;
            case "f2" -> GLFW.GLFW_KEY_F2;
            case "f3" -> GLFW.GLFW_KEY_F3;
            case "f4" -> GLFW.GLFW_KEY_F4;
            case "f5" -> GLFW.GLFW_KEY_F5;
            case "f6" -> GLFW.GLFW_KEY_F6;
            case "f7" -> GLFW.GLFW_KEY_F7;
            case "f8" -> GLFW.GLFW_KEY_F8;
            case "f9" -> GLFW.GLFW_KEY_F9;
            case "f10" -> GLFW.GLFW_KEY_F10;
            case "f11" -> GLFW.GLFW_KEY_F11;
            case "f12" -> GLFW.GLFW_KEY_F12;
            case "insert" -> GLFW.GLFW_KEY_INSERT;
            case "delete" -> GLFW.GLFW_KEY_DELETE;
            case "home" -> GLFW.GLFW_KEY_HOME;
            case "end" -> GLFW.GLFW_KEY_END;
            case "pageup" -> GLFW.GLFW_KEY_PAGE_UP;
            case "pagedown" -> GLFW.GLFW_KEY_PAGE_DOWN;
            case "lshift" -> GLFW.GLFW_KEY_LEFT_SHIFT;
            case "rshift" -> GLFW.GLFW_KEY_RIGHT_SHIFT;
            case "lctrl" -> GLFW.GLFW_KEY_LEFT_CONTROL;
            case "rctrl" -> GLFW.GLFW_KEY_RIGHT_CONTROL;
            case "lalt" -> GLFW.GLFW_KEY_LEFT_ALT;
            case "ralt" -> GLFW.GLFW_KEY_RIGHT_ALT;
            default -> -1;
        };
    }
}
