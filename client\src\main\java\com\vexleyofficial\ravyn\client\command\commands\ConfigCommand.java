package com.vexleyofficial.ravyn.client.command.commands;

import com.vexleyofficial.ravyn.client.RavynClient;
import com.vexleyofficial.ravyn.client.command.Command;
import com.vexleyofficial.ravyn.client.config.ConfigManager;

/**
 * Config command - Manages client configurations
 */
public class ConfigCommand extends Command {
    
    public ConfigCommand() {
        super("config", "Manages client configurations", ".config <save/load/list> [name]", "cfg");
    }
    
    @Override
    public void execute(String[] args) throws Exception {
        if (args.length == 0) {
            sendUsage();
            return;
        }
        
        String action = args[0].toLowerCase();
        
        switch (action) {
            case "save" -> {
                if (args.length != 2) {
                    sendError("Usage: .config save <name>");
                    return;
                }
                String configName = args[1];
                ConfigManager configManager = RavynClient.getInstance().getConfigManager();
                if (configManager.saveConfig(configName)) {
                    sendSuccess("Configuration saved as '" + configName + "'.");
                } else {
                    sendError("Failed to save configuration '" + configName + "'.");
                }
            }
            
            case "load" -> {
                if (args.length != 2) {
                    sendError("Usage: .config load <name>");
                    return;
                }
                String configName = args[1];
                ConfigManager configManager = RavynClient.getInstance().getConfigManager();
                if (configManager.loadConfig(configName)) {
                    sendSuccess("Configuration '" + configName + "' loaded.");
                } else {
                    sendError("Failed to load configuration '" + configName + "'.");
                }
            }
            
            case "list" -> {
                ConfigManager configManager = RavynClient.getInstance().getConfigManager();
                var configs = configManager.getConfigs();
                if (configs.isEmpty()) {
                    sendMessage("§7No configurations found.");
                } else {
                    sendMessage("§7Available configurations:");
                    for (String config : configs) {
                        sendMessage("§f- " + config);
                    }
                }
            }
            
            case "delete" -> {
                if (args.length != 2) {
                    sendError("Usage: .config delete <name>");
                    return;
                }
                String configName = args[1];
                ConfigManager configManager = RavynClient.getInstance().getConfigManager();
                if (configManager.deleteConfig(configName)) {
                    sendSuccess("Configuration '" + configName + "' deleted.");
                } else {
                    sendError("Failed to delete configuration '" + configName + "'.");
                }
            }
            
            default -> sendError("Invalid action. Use save, load, list, or delete.");
        }
    }
}
