package com.vexleyofficial.ravyn.client.command.commands;

import com.vexleyofficial.ravyn.client.command.Command;

/**
 * HClip command - Horizontal teleportation
 */
public class HClipCommand extends Command {
    
    public HClipCommand() {
        super("hclip", "Teleports horizontally", ".hclip <distance>", "hc");
    }
    
    @Override
    public void execute(String[] args) throws Exception {
        if (args.length != 1) {
            sendUsage();
            return;
        }
        
        if (mc.player == null) {
            sendError("You must be in-game to use this command.");
            return;
        }
        
        try {
            double distance = Double.parseDouble(args[0]);
            
            // Calculate new position based on player's facing direction
            float yaw = mc.player.getYaw();
            double radians = Math.toRadians(yaw);
            
            double newX = mc.player.getX() - Math.sin(radians) * distance;
            double newZ = mc.player.getZ() + Math.cos(radians) * distance;
            
            mc.player.setPosition(newX, mc.player.getY(), newZ);
            
            sendSuccess("Teleported §f" + distance + " §ablocks forward.");
            
        } catch (NumberFormatException e) {
            sendError("Invalid distance. Please enter a number.");
        }
    }
}
