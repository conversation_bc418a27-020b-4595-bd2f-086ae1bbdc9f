package com.vexleyofficial.ravyn.client.mixins;

import com.vexleyofficial.ravyn.client.RavynClient;
import net.minecraft.client.gui.screen.ChatScreen;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

/**
 * Mixin for ChatScreen to handle commands
 */
@Mixin(ChatScreen.class)
public class ChatScreenMixin {

    @Inject(method = "sendMessage", at = @At("HEAD"), cancellable = true)
    private void onSendMessage(String chatText, boolean addToHistory, CallbackInfoReturnable<Boolean> cir) {
        if (RavynClient.getInstance() != null && RavynClient.getInstance().getCommandManager() != null) {
            if (RavynClient.getInstance().getCommandManager().processMessage(chatText)) {
                cir.setReturnValue(true);
            }
        }
    }
}
