#!/bin/bash

echo "Building Ravyn Launcher and Client..."

echo ""
echo "[1/3] Building Launcher..."
cd launcher
mvn clean package
if [ $? -ne 0 ]; then
    echo "Failed to build launcher"
    exit 1
fi
cd ..

echo ""
echo "[2/3] Building Client..."
cd client
./gradlew build
if [ $? -ne 0 ]; then
    echo "Failed to build client"
    exit 1
fi
cd ..

echo ""
echo "[3/3] Building Installer..."
cd installer
mvn clean package
if [ $? -ne 0 ]; then
    echo "Failed to build installer"
    exit 1
fi
cd ..

echo ""
echo "Build completed successfully!"
echo ""
echo "Output files:"
echo "- Launcher: launcher/target/ravyn-launcher-1.0.0.jar"
echo "- Client: client/build/libs/ravyn-client-1.0.0.jar"
echo "- Installer: installer/target/ravyn-installer-1.0.0.jar"
echo ""
