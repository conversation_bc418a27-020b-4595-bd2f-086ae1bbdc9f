package com.vexleyofficial.ravyn.launcher.ui;

import com.vexleyofficial.ravyn.launcher.RavynLauncher;
import com.vexleyofficial.ravyn.launcher.auth.AuthenticationManager;
import com.vexleyofficial.ravyn.launcher.game.GameLauncher;
import com.vexleyofficial.ravyn.launcher.util.LauncherConfig;
import com.vexleyofficial.ravyn.launcher.util.Logger;
import com.vexleyofficial.ravyn.launcher.version.VersionManager;
import com.vexleyofficial.ravyn.launcher.version.VersionManifest;
import javafx.application.Platform;
import javafx.collections.FXCollections;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.image.ImageView;
import javafx.scene.layout.VBox;

import java.io.IOException;
import java.net.URL;
import java.util.List;
import java.util.ResourceBundle;
import java.util.concurrent.CompletableFuture;

/**
 * Main launcher UI controller
 */
public class LauncherController implements Initializable {
    
    private static final Logger logger = Logger.getLogger(LauncherController.class);
    
    @FXML private VBox rootPane;
    @FXML private ImageView logoImage;
    @FXML private ComboBox<String> accountComboBox;
    @FXML private ComboBox<String> versionComboBox;
    @FXML private CheckBox ravynClientCheckBox;
    @FXML private Button launchButton;
    @FXML private Button addAccountButton;
    @FXML private Button settingsButton;
    @FXML private ProgressBar progressBar;
    @FXML private Label statusLabel;
    @FXML private TextArea newsArea;
    
    private RavynLauncher launcher;
    private LauncherConfig config;
    private AuthenticationManager authManager;
    private VersionManager versionManager;
    private GameLauncher gameLauncher;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        config = LauncherConfig.getInstance();
        authManager = new AuthenticationManager();
        versionManager = new VersionManager();
        gameLauncher = new GameLauncher();
        
        initializeUI();
        loadAccounts();
        loadVersions();
        loadNews();
    }
    
    public void setLauncher(RavynLauncher launcher) {
        this.launcher = launcher;
    }
    
    private void initializeUI() {
        // Initialize UI components
        statusLabel.setText("Ready");
        progressBar.setVisible(false);
        
        // Set up event handlers
        launchButton.setOnAction(e -> launchGame());
        addAccountButton.setOnAction(e -> showAddAccountDialog());
        settingsButton.setOnAction(e -> showSettingsDialog());
        
        // Load saved settings
        ravynClientCheckBox.setSelected(config.isUseRavynClient());
        ravynClientCheckBox.setOnAction(e -> {
            config.setUseRavynClient(ravynClientCheckBox.isSelected());
            LauncherConfig.save();
        });
        
        // Set up account selection
        accountComboBox.setOnAction(e -> {
            String selectedAccount = accountComboBox.getValue();
            if (selectedAccount != null) {
                config.setSelectedAccount(selectedAccount);
                LauncherConfig.save();
            }
        });
        
        // Set up version selection
        versionComboBox.setOnAction(e -> {
            String selectedVersion = versionComboBox.getValue();
            if (selectedVersion != null) {
                config.setSelectedVersion(selectedVersion);
                LauncherConfig.save();
            }
        });
    }
    
    private void loadAccounts() {
        List<LauncherConfig.Account> accounts = config.getAccounts();
        
        if (accounts.isEmpty()) {
            // Add default offline account
            LauncherConfig.Account offlineAccount = new LauncherConfig.Account("Player", "offline", true);
            accounts.add(offlineAccount);
            config.setAccounts(accounts);
            config.setSelectedAccount("Player");
            LauncherConfig.save();
        }
        
        accountComboBox.setItems(FXCollections.observableArrayList(
            accounts.stream().map(LauncherConfig.Account::getUsername).toList()
        ));
        
        // Select saved account
        String selectedAccount = config.getSelectedAccount();
        if (selectedAccount != null && !selectedAccount.isEmpty()) {
            accountComboBox.setValue(selectedAccount);
        } else if (!accounts.isEmpty()) {
            accountComboBox.setValue(accounts.get(0).getUsername());
        }
    }
    
    private void loadVersions() {
        setStatus("Loading available versions...", true);
        
        versionManager.getAvailableVersions().thenAccept(versions -> {
            Platform.runLater(() -> {
                List<String> versionIds = versions.stream()
                    .map(v -> v.id)
                    .toList();
                
                versionComboBox.setItems(FXCollections.observableArrayList(versionIds));
                
                // Select saved version
                String selectedVersion = config.getSelectedVersion();
                if (selectedVersion != null && versionIds.contains(selectedVersion)) {
                    versionComboBox.setValue(selectedVersion);
                } else if (!versionIds.isEmpty()) {
                    versionComboBox.setValue(versionIds.get(0));
                }
                
                setStatus("Ready", false);
            });
        }).exceptionally(throwable -> {
            Platform.runLater(() -> {
                logger.error("Failed to load versions", throwable);
                setStatus("Failed to load versions", false);
                showError("Failed to load versions", throwable.getMessage());
            });
            return null;
        });
    }
    
    private void loadNews() {
        // TODO: Load news from server or local file
        newsArea.setText("Welcome to Ravyn Launcher!\n\n" +
            "Features:\n" +
            "• Custom Minecraft launcher\n" +
            "• Hacked client support\n" +
            "• Multi-version support (1.8.9 - 1.21.5)\n" +
            "• Account management\n" +
            "• Modern UI\n\n" +
            "Get started by selecting your account and Minecraft version, then click Launch!");
    }
    
    @FXML
    private void launchGame() {
        String selectedAccount = accountComboBox.getValue();
        String selectedVersion = versionComboBox.getValue();
        
        if (selectedAccount == null || selectedVersion == null) {
            showError("Launch Error", "Please select an account and version");
            return;
        }
        
        // Find account
        LauncherConfig.Account account = config.getAccounts().stream()
            .filter(a -> a.getUsername().equals(selectedAccount))
            .findFirst()
            .orElse(null);
        
        if (account == null) {
            showError("Launch Error", "Selected account not found");
            return;
        }
        
        // Check if version is installed
        if (!versionManager.isVersionInstalled(selectedVersion)) {
            installAndLaunch(selectedVersion, account);
        } else {
            launchMinecraft(selectedVersion, account);
        }
    }
    
    private void installAndLaunch(String version, LauncherConfig.Account account) {
        setStatus("Installing Minecraft " + version + "...", true);
        launchButton.setDisabled(true);
        
        versionManager.installVersion(version, new VersionManager.ProgressCallback() {
            @Override
            public void updateProgress(String message, int percentage) {
                Platform.runLater(() -> {
                    setStatus(message, true);
                    progressBar.setProgress(percentage / 100.0);
                });
            }
        }).thenAccept(success -> {
            Platform.runLater(() -> {
                if (success) {
                    launchMinecraft(version, account);
                } else {
                    setStatus("Installation failed", false);
                    launchButton.setDisabled(false);
                    showError("Installation Error", "Failed to install Minecraft " + version);
                }
            });
        });
    }
    
    private void launchMinecraft(String version, LauncherConfig.Account account) {
        setStatus("Launching Minecraft...", true);
        
        CompletableFuture.supplyAsync(() -> {
            try {
                VersionManifest manifest = versionManager.getInstalledVersion(version);
                if (manifest == null) {
                    throw new RuntimeException("Version manifest not found");
                }
                
                GameLauncher.LaunchConfig launchConfig = new GameLauncher.LaunchConfig(version, manifest, account);
                Process gameProcess = gameLauncher.launchGame(launchConfig);
                
                return gameProcess;
            } catch (Exception e) {
                logger.error("Failed to launch game", e);
                throw new RuntimeException(e);
            }
        }).thenAccept(process -> {
            Platform.runLater(() -> {
                setStatus("Game launched successfully", false);
                launchButton.setDisabled(false);
                
                // Optionally minimize launcher
                launcher.getPrimaryStage().setIconified(true);
            });
        }).exceptionally(throwable -> {
            Platform.runLater(() -> {
                setStatus("Launch failed", false);
                launchButton.setDisabled(false);
                showError("Launch Error", "Failed to launch Minecraft: " + throwable.getMessage());
            });
            return null;
        });
    }
    
    private void showAddAccountDialog() {
        // TODO: Implement add account dialog
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Add Account");
        alert.setHeaderText("Account Management");
        alert.setContentText("Account management dialog will be implemented here.\n\n" +
            "For now, you can use the default offline account 'Player'.");
        alert.showAndWait();
    }
    
    private void showSettingsDialog() {
        // TODO: Implement settings dialog
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Settings");
        alert.setHeaderText("Launcher Settings");
        alert.setContentText("Settings dialog will be implemented here.\n\n" +
            "Current settings are saved automatically.");
        alert.showAndWait();
    }
    
    private void setStatus(String message, boolean showProgress) {
        statusLabel.setText(message);
        progressBar.setVisible(showProgress);
        if (!showProgress) {
            progressBar.setProgress(0);
        }
    }
    
    private void showError(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    public void cleanup() {
        try {
            if (authManager != null) authManager.close();
            if (versionManager != null) versionManager.close();
        } catch (IOException e) {
            logger.error("Error during cleanup", e);
        }
    }
}
