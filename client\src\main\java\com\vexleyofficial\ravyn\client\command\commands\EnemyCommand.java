package com.vexleyofficial.ravyn.client.command.commands;

import com.vexleyofficial.ravyn.client.command.Command;

/**
 * Enemy command - Manages enemies list
 */
public class EnemyCommand extends Command {
    
    public EnemyCommand() {
        super("enemy", "Manages enemies list", ".enemy <add/remove/list> [player]", "e");
    }
    
    @Override
    public void execute(String[] args) throws Exception {
        if (args.length == 0) {
            sendUsage();
            return;
        }
        
        String action = args[0].toLowerCase();
        
        switch (action) {
            case "add" -> {
                if (args.length != 2) {
                    sendError("Usage: .enemy add <player>");
                    return;
                }
                String playerName = args[1];
                // TODO: Implement enemy management
                sendSuccess("Added §f" + playerName + " §ato enemies list.");
            }
            
            case "remove", "del" -> {
                if (args.length != 2) {
                    sendError("Usage: .enemy remove <player>");
                    return;
                }
                String playerName = args[1];
                sendSuccess("Removed §f" + playerName + " §afrom enemies list.");
            }
            
            case "list" -> {
                sendMessage("§7Enemies list is empty.");
            }
            
            case "clear" -> {
                sendSuccess("Cleared enemies list.");
            }
            
            default -> sendError("Invalid action. Use add, remove, list, or clear.");
        }
    }
}
