package com.vexleyofficial.ravyn.client.module;

import com.vexleyofficial.ravyn.client.module.modules.combat.*;
import com.vexleyofficial.ravyn.client.module.modules.movement.*;
import com.vexleyofficial.ravyn.client.module.modules.utility.*;
import com.vexleyofficial.ravyn.client.module.modules.visual.*;
import com.vexleyofficial.ravyn.client.util.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Manages all client modules
 */
public class ModuleManager {
    
    private static final Logger logger = Logger.getLogger(ModuleManager.class);
    
    private final List<Module> modules = new ArrayList<>();
    
    public ModuleManager() {
        logger.info("Initializing modules...");
        
        // Combat modules
        addModule(new KillAura());
        addModule(new Reach());
        addModule(new AutoClicker());
        addModule(new Criticals());
        addModule(new Velocity());
        addModule(new AntiBot());
        
        // Movement modules
        addModule(new Fly());
        addModule(new Speed());
        addModule(new NoFall());
        addModule(new Jesus());
        addModule(new Sprint());
        
        // Visual modules
        addModule(new ESP());
        addModule(new Tracers());
        addModule(new Fullbright());
        addModule(new XRay());
        addModule(new ChestESP());
        addModule(new HUD());
        
        // Utility modules
        addModule(new AutoMine());
        addModule(new Scaffold());
        addModule(new Timer());
        addModule(new FastPlace());
        addModule(new AutoTool());
        
        logger.info("Loaded {} modules", modules.size());
    }
    
    private void addModule(Module module) {
        modules.add(module);
        logger.debug("Added module: {}", module.getName());
    }
    
    /**
     * Called every tick to update enabled modules
     */
    public void onTick() {
        for (Module module : modules) {
            if (module.isEnabled()) {
                try {
                    module.onTick();
                } catch (Exception e) {
                    logger.error("Error in module tick: " + module.getName(), e);
                }
            }
        }
    }
    
    /**
     * Get module by name
     */
    public Module getModule(String name) {
        return modules.stream()
            .filter(module -> module.getName().equalsIgnoreCase(name))
            .findFirst()
            .orElse(null);
    }
    
    /**
     * Get module by class
     */
    @SuppressWarnings("unchecked")
    public <T extends Module> T getModule(Class<T> clazz) {
        return (T) modules.stream()
            .filter(module -> module.getClass() == clazz)
            .findFirst()
            .orElse(null);
    }
    
    /**
     * Get modules by category
     */
    public List<Module> getModulesByCategory(Module.Category category) {
        return modules.stream()
            .filter(module -> module.getCategory() == category)
            .collect(Collectors.toList());
    }
    
    /**
     * Get all enabled modules
     */
    public List<Module> getEnabledModules() {
        return modules.stream()
            .filter(Module::isEnabled)
            .collect(Collectors.toList());
    }
    
    /**
     * Get all modules
     */
    public List<Module> getModules() {
        return new ArrayList<>(modules);
    }
    
    /**
     * Toggle module by name
     */
    public void toggleModule(String name) {
        Module module = getModule(name);
        if (module != null) {
            module.toggle();
        }
    }
    
    /**
     * Handle key press
     */
    public void onKeyPress(int key) {
        for (Module module : modules) {
            if (module.getKey() == key) {
                module.toggle();
            }
        }
    }
}
