package com.vexleyofficial.ravyn.client.module.modules.utility;

import com.vexleyofficial.ravyn.client.module.Module;
import com.vexleyofficial.ravyn.client.setting.BooleanSetting;
import com.vexleyofficial.ravyn.client.setting.NumberSetting;

/**
 * Scaffold module - Automatically places blocks under you
 */
public class Scaffold extends Module {
    
    private final NumberSetting delay = new NumberSetting("Delay", 100, 0, 500, 10);
    private final BooleanSetting tower = new BooleanSetting("Tower", false);
    private final BooleanSetting safeWalk = new BooleanSetting("SafeWalk", true);
    
    public Scaffold() {
        super("Scaffold", "Automatically places blocks under you", Category.UTILITY);
        addSetting(delay);
        addSetting(tower);
        addSetting(safeWalk);
    }
}
