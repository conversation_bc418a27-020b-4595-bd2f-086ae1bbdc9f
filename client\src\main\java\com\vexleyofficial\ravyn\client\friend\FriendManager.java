package com.vexleyofficial.ravyn.client.friend;

import com.vexleyofficial.ravyn.client.util.Logger;
import net.minecraft.entity.player.PlayerEntity;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Manages friends and enemies lists
 */
public class FriendManager {
    
    private static final Logger logger = Logger.getLogger(FriendManager.class);
    
    private final List<String> friends = new CopyOnWriteArrayList<>();
    private final List<String> enemies = new CopyOnWriteArrayList<>();
    
    public FriendManager() {
        logger.info("Friend manager initialized");
    }
    
    /**
     * Add a friend
     */
    public boolean addFriend(String username) {
        if (username == null || username.trim().isEmpty()) {
            return false;
        }
        
        String name = username.trim();
        if (friends.contains(name)) {
            return false;
        }
        
        friends.add(name);
        // Remove from enemies if present
        enemies.remove(name);
        
        logger.debug("Added friend: {}", name);
        return true;
    }
    
    /**
     * Remove a friend
     */
    public boolean removeFriend(String username) {
        if (username == null) {
            return false;
        }
        
        boolean removed = friends.remove(username.trim());
        if (removed) {
            logger.debug("Removed friend: {}", username);
        }
        return removed;
    }
    
    /**
     * Check if a player is a friend
     */
    public boolean isFriend(String username) {
        return username != null && friends.contains(username.trim());
    }
    
    /**
     * Check if a player entity is a friend
     */
    public boolean isFriend(PlayerEntity player) {
        return player != null && isFriend(player.getGameProfile().getName());
    }
    
    /**
     * Add an enemy
     */
    public boolean addEnemy(String username) {
        if (username == null || username.trim().isEmpty()) {
            return false;
        }
        
        String name = username.trim();
        if (enemies.contains(name)) {
            return false;
        }
        
        enemies.add(name);
        // Remove from friends if present
        friends.remove(name);
        
        logger.debug("Added enemy: {}", name);
        return true;
    }
    
    /**
     * Remove an enemy
     */
    public boolean removeEnemy(String username) {
        if (username == null) {
            return false;
        }
        
        boolean removed = enemies.remove(username.trim());
        if (removed) {
            logger.debug("Removed enemy: {}", username);
        }
        return removed;
    }
    
    /**
     * Check if a player is an enemy
     */
    public boolean isEnemy(String username) {
        return username != null && enemies.contains(username.trim());
    }
    
    /**
     * Check if a player entity is an enemy
     */
    public boolean isEnemy(PlayerEntity player) {
        return player != null && isEnemy(player.getGameProfile().getName());
    }
    
    /**
     * Get all friends
     */
    public List<String> getFriends() {
        return new ArrayList<>(friends);
    }
    
    /**
     * Get all enemies
     */
    public List<String> getEnemies() {
        return new ArrayList<>(enemies);
    }
    
    /**
     * Clear all friends
     */
    public void clearFriends() {
        friends.clear();
        logger.debug("Cleared friends list");
    }
    
    /**
     * Clear all enemies
     */
    public void clearEnemies() {
        enemies.clear();
        logger.debug("Cleared enemies list");
    }
    
    /**
     * Get friend/enemy status for display
     */
    public String getRelationStatus(String username) {
        if (isFriend(username)) {
            return "§aFriend";
        } else if (isEnemy(username)) {
            return "§cEnemy";
        } else {
            return "§7Unknown";
        }
    }
    
    /**
     * Get friend/enemy status for player entity
     */
    public String getRelationStatus(PlayerEntity player) {
        return player != null ? getRelationStatus(player.getGameProfile().getName()) : "§7Unknown";
    }
}
