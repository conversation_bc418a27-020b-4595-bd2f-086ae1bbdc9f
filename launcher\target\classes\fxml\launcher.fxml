<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox fx:id="rootPane" maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" 
      prefHeight="600.0" prefWidth="900.0" styleClass="root-pane" xmlns="http://javafx.com/javafx/11.0.1" 
      xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.vexleyofficial.ravyn.launcher.ui.LauncherController">
   
   <!-- Header -->
   <HBox alignment="CENTER" prefHeight="100.0" styleClass="header-pane">
      <children>
         <ImageView fx:id="logoImage" fitHeight="80.0" fitWidth="200.0" pickOnBounds="true" preserveRatio="true" />
         <Region HBox.hgrow="ALWAYS" />
         <Label styleClass="version-label" text="v1.0.0">
            <font>
               <Font size="12.0" />
            </font>
         </Label>
      </children>
      <padding>
         <Insets bottom="10.0" left="20.0" right="20.0" top="10.0" />
      </padding>
   </HBox>
   
   <!-- Main Content -->
   <HBox prefHeight="400.0" VBox.vgrow="ALWAYS">
      <children>
         
         <!-- Left Panel - Game Settings -->
         <VBox prefWidth="300.0" spacing="15.0" styleClass="left-panel">
            <children>
               
               <!-- Account Selection -->
               <VBox spacing="5.0">
                  <children>
                     <Label styleClass="section-label" text="Account" />
                     <HBox spacing="10.0">
                        <children>
                           <ComboBox fx:id="accountComboBox" prefWidth="200.0" promptText="Select Account" />
                           <Button fx:id="addAccountButton" styleClass="secondary-button" text="+" />
                        </children>
                     </HBox>
                  </children>
               </VBox>
               
               <!-- Version Selection -->
               <VBox spacing="5.0">
                  <children>
                     <Label styleClass="section-label" text="Version" />
                     <ComboBox fx:id="versionComboBox" prefWidth="250.0" promptText="Select Version" />
                  </children>
               </VBox>
               
               <!-- Client Options -->
               <VBox spacing="5.0">
                  <children>
                     <Label styleClass="section-label" text="Client" />
                     <CheckBox fx:id="ravynClientCheckBox" styleClass="client-checkbox" text="Use Ravyn Client (Hacked)" />
                  </children>
               </VBox>
               
               <!-- Launch Button -->
               <Region VBox.vgrow="ALWAYS" />
               <Button fx:id="launchButton" prefHeight="50.0" prefWidth="250.0" styleClass="launch-button" text="LAUNCH GAME">
                  <font>
                     <Font name="System Bold" size="16.0" />
                  </font>
               </Button>
               
               <!-- Settings Button -->
               <Button fx:id="settingsButton" prefWidth="250.0" styleClass="secondary-button" text="Settings" />
               
            </children>
            <padding>
               <Insets bottom="20.0" left="20.0" right="10.0" top="20.0" />
            </padding>
         </VBox>
         
         <!-- Right Panel - News and Info -->
         <VBox prefWidth="600.0" spacing="10.0" styleClass="right-panel" HBox.hgrow="ALWAYS">
            <children>
               
               <!-- News Section -->
               <VBox spacing="5.0" VBox.vgrow="ALWAYS">
                  <children>
                     <Label styleClass="section-label" text="News &amp; Updates" />
                     <TextArea fx:id="newsArea" editable="false" prefHeight="300.0" styleClass="news-area" 
                               wrapText="true" VBox.vgrow="ALWAYS" />
                  </children>
               </VBox>
               
               <!-- Features Section -->
               <VBox spacing="5.0">
                  <children>
                     <Label styleClass="section-label" text="Ravyn Client Features" />
                     <GridPane hgap="10.0" vgap="5.0">
                        <columnConstraints>
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                           <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                        </columnConstraints>
                        <rowConstraints>
                           <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                           <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                           <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                        </rowConstraints>
                        <children>
                           <Label styleClass="feature-label" text="• Combat: KillAura, Reach, Criticals" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                           <Label styleClass="feature-label" text="• Movement: Fly, Speed, NoFall" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                           <Label styleClass="feature-label" text="• Visual: ESP, Tracers, Fullbright" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                           <Label styleClass="feature-label" text="• Utility: AutoMine, Scaffold, Timer" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                           <Label styleClass="feature-label" text="• Bypass: Anti-cheat protection" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                           <Label styleClass="feature-label" text="• GUI: Modern ClickGUI interface" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        </children>
                     </GridPane>
                  </children>
               </VBox>
               
            </children>
            <padding>
               <Insets bottom="20.0" left="10.0" right="20.0" top="20.0" />
            </padding>
         </VBox>
         
      </children>
   </HBox>
   
   <!-- Footer -->
   <HBox alignment="CENTER_LEFT" prefHeight="50.0" spacing="10.0" styleClass="footer-pane">
      <children>
         <Label fx:id="statusLabel" styleClass="status-label" text="Ready" />
         <Region HBox.hgrow="ALWAYS" />
         <ProgressBar fx:id="progressBar" prefWidth="200.0" visible="false" />
      </children>
      <padding>
         <Insets bottom="10.0" left="20.0" right="20.0" top="10.0" />
      </padding>
   </HBox>
   
</VBox>
