package com.vexleyofficial.ravyn.client.module.modules.combat;

import com.vexleyofficial.ravyn.client.module.Module;
import com.vexleyofficial.ravyn.client.setting.BooleanSetting;
import com.vexleyofficial.ravyn.client.setting.NumberSetting;
import net.minecraft.entity.player.PlayerEntity;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * AntiBot module - Detects and ignores bot players
 */
public class AntiBot extends Module {
    
    private final BooleanSetting tabCheck = new BooleanSetting("Tab Check", true);
    private final BooleanSetting pingCheck = new BooleanSetting("Ping Check", true);
    private final BooleanSetting nameCheck = new BooleanSetting("Name Check", true);
    private final BooleanSetting duplicateCheck = new BooleanSetting("Duplicate Check", true);
    private final NumberSetting maxPing = new NumberSetting("Max Ping", 500, 0, 1000, 10);
    
    private final Map<UUID, Long> playerJoinTimes = new HashMap<>();
    private final Map<String, Integer> nameOccurrences = new HashMap<>();
    
    public AntiBot() {
        super("AntiBot", "Detects and ignores bot players", Category.COMBAT);
        
        addSetting(tabCheck);
        addSetting(pingCheck);
        addSetting(nameCheck);
        addSetting(duplicateCheck);
        addSetting(maxPing);
    }
    
    /**
     * Check if a player is likely a bot
     */
    public boolean isBot(PlayerEntity player) {
        if (!isEnabled() || player == null || player == mc.player) {
            return false;
        }
        
        // Tab list check
        if (tabCheck.getValue() && !isInTabList(player)) {
            return true;
        }
        
        // Ping check
        if (pingCheck.getValue() && hasInvalidPing(player)) {
            return true;
        }
        
        // Name pattern check
        if (nameCheck.getValue() && hasInvalidName(player)) {
            return true;
        }
        
        // Duplicate name check
        if (duplicateCheck.getValue() && hasDuplicateName(player)) {
            return true;
        }
        
        return false;
    }
    
    private boolean isInTabList(PlayerEntity player) {
        if (mc.getNetworkHandler() == null) return true;
        return mc.getNetworkHandler().getPlayerListEntry(player.getUuid()) != null;
    }
    
    private boolean hasInvalidPing(PlayerEntity player) {
        if (mc.getNetworkHandler() == null) return false;
        
        var playerListEntry = mc.getNetworkHandler().getPlayerListEntry(player.getUuid());
        if (playerListEntry == null) return true;
        
        int ping = playerListEntry.getLatency();
        return ping <= 0 || ping > maxPing.getValue();
    }
    
    private boolean hasInvalidName(PlayerEntity player) {
        String name = player.getGameProfile().getName();
        
        // Check for common bot name patterns
        if (name.matches(".*[0-9]{3,}.*")) { // Names with 3+ consecutive numbers
            return true;
        }
        
        if (name.matches("^[a-zA-Z]{1,2}[0-9]+$")) { // Short names with numbers
            return true;
        }
        
        if (name.length() < 3 || name.length() > 16) { // Invalid name length
            return true;
        }
        
        // Check for random character patterns
        if (name.matches(".*[qxz]{2,}.*")) { // Multiple uncommon letters
            return true;
        }
        
        return false;
    }
    
    private boolean hasDuplicateName(PlayerEntity player) {
        String name = player.getGameProfile().getName();
        
        // Count occurrences of similar names
        nameOccurrences.put(name, nameOccurrences.getOrDefault(name, 0) + 1);
        
        // Check for names with slight variations (common bot behavior)
        long similarNames = nameOccurrences.keySet().stream()
            .filter(n -> isSimilarName(name, n))
            .count();
        
        return similarNames > 3; // More than 3 similar names is suspicious
    }
    
    private boolean isSimilarName(String name1, String name2) {
        if (name1.equals(name2)) return false;
        
        // Check if names are similar (differ by 1-2 characters)
        if (Math.abs(name1.length() - name2.length()) > 2) return false;
        
        int differences = 0;
        int minLength = Math.min(name1.length(), name2.length());
        
        for (int i = 0; i < minLength; i++) {
            if (name1.charAt(i) != name2.charAt(i)) {
                differences++;
                if (differences > 2) return false;
            }
        }
        
        return differences <= 2;
    }
    
    @Override
    public void onEnable() {
        super.onEnable();
        playerJoinTimes.clear();
        nameOccurrences.clear();
    }
    
    @Override
    public void onTick() {
        if (mc.world == null) return;
        
        // Track player join times
        for (PlayerEntity player : mc.world.getPlayers()) {
            UUID uuid = player.getUuid();
            if (!playerJoinTimes.containsKey(uuid)) {
                playerJoinTimes.put(uuid, System.currentTimeMillis());
            }
        }
        
        // Clean up old entries
        playerJoinTimes.entrySet().removeIf(entry -> 
            System.currentTimeMillis() - entry.getValue() > 300000); // 5 minutes
    }
}
