/* <PERSON><PERSON>er CSS Stylesheet */

/* Root styling */
.root {
    -fx-font-family: "Segoe UI", "Arial", sans-serif;
    -fx-font-size: 14px;
}

/* Color scheme */
.root-pane {
    -fx-background-color: linear-gradient(to bottom, #1a1a2e, #16213e);
}

.header-pane {
    -fx-background-color: linear-gradient(to right, #0f3460, #16537e);
    -fx-border-color: #16537e;
    -fx-border-width: 0 0 2 0;
}

.left-panel {
    -fx-background-color: rgba(255, 255, 255, 0.05);
    -fx-border-color: rgba(255, 255, 255, 0.1);
    -fx-border-width: 0 1 0 0;
}

.right-panel {
    -fx-background-color: rgba(255, 255, 255, 0.02);
}

.footer-pane {
    -fx-background-color: rgba(0, 0, 0, 0.3);
    -fx-border-color: rgba(255, 255, 255, 0.1);
    -fx-border-width: 1 0 0 0;
}

/* Text styling */
.version-label {
    -fx-text-fill: rgba(255, 255, 255, 0.7);
}

.section-label {
    -fx-text-fill: #ffffff;
    -fx-font-weight: bold;
    -fx-font-size: 16px;
}

.feature-label {
    -fx-text-fill: rgba(255, 255, 255, 0.8);
    -fx-font-size: 12px;
}

.status-label {
    -fx-text-fill: rgba(255, 255, 255, 0.9);
}

/* Button styling */
.launch-button {
    -fx-background-color: linear-gradient(to bottom, #e74c3c, #c0392b);
    -fx-text-fill: white;
    -fx-background-radius: 8;
    -fx-border-radius: 8;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.3), 5, 0, 0, 2);
}

.launch-button:hover {
    -fx-background-color: linear-gradient(to bottom, #ec7063, #e74c3c);
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.4), 8, 0, 0, 3);
}

.launch-button:pressed {
    -fx-background-color: linear-gradient(to bottom, #c0392b, #a93226);
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 3, 0, 0, 1);
}

.launch-button:disabled {
    -fx-background-color: linear-gradient(to bottom, #7f8c8d, #95a5a6);
    -fx-text-fill: rgba(255, 255, 255, 0.5);
    -fx-cursor: default;
    -fx-effect: none;
}

.secondary-button {
    -fx-background-color: linear-gradient(to bottom, #3498db, #2980b9);
    -fx-text-fill: white;
    -fx-background-radius: 5;
    -fx-border-radius: 5;
    -fx-cursor: hand;
}

.secondary-button:hover {
    -fx-background-color: linear-gradient(to bottom, #5dade2, #3498db);
}

.secondary-button:pressed {
    -fx-background-color: linear-gradient(to bottom, #2980b9, #21618c);
}

/* ComboBox styling */
.combo-box {
    -fx-background-color: rgba(255, 255, 255, 0.1);
    -fx-text-fill: white;
    -fx-background-radius: 5;
    -fx-border-color: rgba(255, 255, 255, 0.2);
    -fx-border-radius: 5;
    -fx-border-width: 1;
}

.combo-box:focused {
    -fx-border-color: #3498db;
    -fx-border-width: 2;
}

.combo-box .list-cell {
    -fx-text-fill: white;
    -fx-background-color: transparent;
}

.combo-box-popup .list-view {
    -fx-background-color: #2c3e50;
    -fx-border-color: #3498db;
    -fx-border-width: 1;
}

.combo-box-popup .list-view .list-cell {
    -fx-text-fill: white;
    -fx-background-color: transparent;
}

.combo-box-popup .list-view .list-cell:hover {
    -fx-background-color: rgba(52, 152, 219, 0.3);
}

.combo-box-popup .list-view .list-cell:selected {
    -fx-background-color: #3498db;
}

/* CheckBox styling */
.client-checkbox {
    -fx-text-fill: white;
}

.client-checkbox .box {
    -fx-background-color: rgba(255, 255, 255, 0.1);
    -fx-border-color: rgba(255, 255, 255, 0.3);
    -fx-border-radius: 3;
    -fx-background-radius: 3;
}

.client-checkbox:selected .box {
    -fx-background-color: #e74c3c;
    -fx-border-color: #c0392b;
}

.client-checkbox .mark {
    -fx-background-color: white;
}

/* TextArea styling */
.news-area {
    -fx-background-color: rgba(255, 255, 255, 0.05);
    -fx-text-fill: rgba(255, 255, 255, 0.9);
    -fx-border-color: rgba(255, 255, 255, 0.1);
    -fx-border-radius: 5;
    -fx-background-radius: 5;
    -fx-border-width: 1;
}

.news-area .content {
    -fx-background-color: transparent;
}

.news-area .scroll-pane {
    -fx-background-color: transparent;
}

.news-area .scroll-pane .viewport {
    -fx-background-color: transparent;
}

.news-area .scroll-bar {
    -fx-background-color: rgba(255, 255, 255, 0.1);
}

.news-area .scroll-bar .thumb {
    -fx-background-color: rgba(255, 255, 255, 0.3);
    -fx-background-radius: 5;
}

.news-area .scroll-bar .thumb:hover {
    -fx-background-color: rgba(255, 255, 255, 0.5);
}

/* ProgressBar styling */
.progress-bar {
    -fx-background-color: rgba(255, 255, 255, 0.1);
    -fx-background-radius: 10;
}

.progress-bar .bar {
    -fx-background-color: linear-gradient(to right, #3498db, #2980b9);
    -fx-background-radius: 10;
    -fx-background-insets: 1;
}

.progress-bar .track {
    -fx-background-color: rgba(255, 255, 255, 0.1);
    -fx-background-radius: 10;
}

/* Tooltip styling */
.tooltip {
    -fx-background-color: #2c3e50;
    -fx-text-fill: white;
    -fx-background-radius: 5;
    -fx-border-color: #3498db;
    -fx-border-radius: 5;
    -fx-border-width: 1;
    -fx-font-size: 12px;
}

/* Alert dialog styling */
.alert {
    -fx-background-color: #2c3e50;
}

.alert .header-panel {
    -fx-background-color: #34495e;
}

.alert .header-panel .label {
    -fx-text-fill: white;
}

.alert .content.label {
    -fx-text-fill: white;
}

/* Scrollbar styling */
.scroll-bar {
    -fx-background-color: rgba(255, 255, 255, 0.05);
}

.scroll-bar .thumb {
    -fx-background-color: rgba(255, 255, 255, 0.2);
    -fx-background-radius: 5;
}

.scroll-bar .thumb:hover {
    -fx-background-color: rgba(255, 255, 255, 0.4);
}

.scroll-bar .increment-button,
.scroll-bar .decrement-button {
    -fx-background-color: transparent;
}

.scroll-bar .increment-arrow,
.scroll-bar .decrement-arrow {
    -fx-background-color: rgba(255, 255, 255, 0.3);
}
