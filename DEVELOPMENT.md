# Ravyn Development Guide

## Project Structure

```
Ravyn/
├── launcher/           # JavaFX-based launcher application
│   ├── src/main/java/  # Launcher source code
│   ├── src/main/resources/ # FXML, CSS, and assets
│   └── pom.xml         # Maven configuration
├── client/             # Fabric-based hacked client
│   ├── src/main/java/  # Client source code
│   ├── src/main/resources/ # Client resources
│   ├── build.gradle    # Gradle configuration
│   └── gradle.properties # Gradle properties
├── installer/          # JavaFX-based installer
│   ├── src/main/java/  # Installer source code
│   └── pom.xml         # Maven configuration
├── build.bat          # Windows build script
├── build.sh           # Linux/Mac build script
└── README.md          # Project documentation
```

## Development Setup

### Prerequisites

1. **Java Development Kit (JDK) 17+**
   - Download from [Oracle](https://www.oracle.com/java/technologies/downloads/) or [OpenJDK](https://openjdk.org/)
   - Set `JAVA_HOME` environment variable

2. **Apache Maven 3.6+**
   - Download from [Maven website](https://maven.apache.org/download.cgi)
   - Add to system PATH

3. **Gradle 7.0+** (included via wrapper)
   - No separate installation needed

4. **IDE (Recommended: IntelliJ IDEA)**
   - Download [IntelliJ IDEA](https://www.jetbrains.com/idea/)
   - Install Minecraft Development plugin for better support

### Building the Project

#### Option 1: Using Build Scripts

**Windows:**
```cmd
build.bat
```

**Linux/Mac:**
```bash
chmod +x build.sh
./build.sh
```

#### Option 2: Manual Building

**Build Launcher:**
```bash
cd launcher
mvn clean package
```

**Build Client:**
```bash
cd client
./gradlew build
```

**Build Installer:**
```bash
cd installer
mvn clean package
```

## Development Workflow

### 1. Launcher Development

The launcher is built with JavaFX and Maven. Key components:

- **Main Class:** `RavynLauncher.java`
- **UI Controller:** `LauncherController.java`
- **Authentication:** `AuthenticationManager.java`
- **Version Management:** `VersionManager.java`
- **Game Launching:** `GameLauncher.java`

**Running the launcher:**
```bash
cd launcher
mvn javafx:run
```

### 2. Client Development

The client is built with Fabric and Gradle. Key components:

- **Main Class:** `RavynClient.java`
- **Module System:** `ModuleManager.java` and `Module.java`
- **Event System:** `EventManager.java`
- **GUI System:** `ClickGui.java`
- **Mixins:** Located in `mixins/` package

**Running the client:**
```bash
cd client
./gradlew runClient
```

### 3. Testing

**Test Launcher:**
```bash
cd launcher
mvn test
```

**Test Client:**
```bash
cd client
./gradlew test
```

## Adding New Features

### Adding a New Module

1. Create a new class extending `Module` in the appropriate category package
2. Implement required methods (`onEnable`, `onDisable`, `onTick`)
3. Add settings using `addSetting()`
4. Register the module in `ModuleManager.java`

Example:
```java
public class ExampleModule extends Module {
    private final BooleanSetting enabled = new BooleanSetting("Enabled", true);
    
    public ExampleModule() {
        super("Example", "Example module", Category.UTILITY);
        addSetting(enabled);
    }
    
    @Override
    public void onTick() {
        // Module logic here
    }
}
```

### Adding a New Mixin

1. Create a new mixin class in the `mixins/` package
2. Add the mixin to `ravyn-client.mixins.json`
3. Use `@Inject`, `@Redirect`, or other mixin annotations

Example:
```java
@Mixin(SomeMinecraftClass.class)
public class SomeMinecraftClassMixin {
    @Inject(method = "someMethod", at = @At("HEAD"))
    private void onSomeMethod(CallbackInfo ci) {
        // Injection logic here
    }
}
```

## Debugging

### Launcher Debugging

1. Run with debug arguments:
```bash
mvn javafx:run -Djavafx.args="--debug"
```

2. Use IDE debugger by running `RavynLauncher.main()`

### Client Debugging

1. Run in development environment:
```bash
./gradlew runClient
```

2. Use Minecraft's debug features (F3 menu, logs)

3. Check client logs in `.minecraft/logs/`

## Code Style

### Java Conventions

- Use 4 spaces for indentation
- Follow standard Java naming conventions
- Add JavaDoc comments for public methods
- Keep methods under 50 lines when possible

### Mixin Conventions

- Name mixins with `Mixin` suffix
- Use descriptive injection point names
- Add comments explaining what the mixin does
- Test mixins thoroughly to avoid crashes

## Common Issues

### Build Issues

**Maven dependency issues:**
```bash
mvn dependency:resolve
mvn clean install
```

**Gradle dependency issues:**
```bash
./gradlew clean build --refresh-dependencies
```

### Runtime Issues

**JavaFX module issues:**
- Ensure JavaFX is properly configured in module path
- Check JVM arguments for JavaFX modules

**Mixin issues:**
- Verify mixin mappings are correct
- Check that target methods exist in the Minecraft version
- Ensure mixin configuration is valid

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

### Pull Request Guidelines

- Include clear description of changes
- Add tests for new features
- Follow existing code style
- Update documentation if needed

## Security Considerations

### Client Safety

- Never include malicious code
- Respect server rules and anti-cheat systems
- Provide clear warnings about hack usage
- Include bypass detection where possible

### Authentication

- Store tokens securely
- Implement proper OAuth2 flow
- Never log sensitive information
- Use HTTPS for all network requests

## Release Process

1. Update version numbers in all `pom.xml` and `gradle.properties`
2. Build all components
3. Test installation and functionality
4. Create GitHub release with binaries
5. Update documentation

## Support

- **Issues:** [GitHub Issues](https://github.com/vexleyofficial/Ravyn/issues)
- **Discussions:** [GitHub Discussions](https://github.com/vexleyofficial/Ravyn/discussions)
- **Discord:** [Join our server](https://discord.gg/ravyn)

## License

This project is licensed under the MIT License. See [LICENSE](LICENSE) for details.
