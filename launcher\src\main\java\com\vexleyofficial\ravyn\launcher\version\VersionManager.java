package com.vexleyofficial.ravyn.launcher.version;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vexleyofficial.ravyn.launcher.util.LauncherConfig;
import com.vexleyofficial.ravyn.launcher.util.Logger;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Manages Minecraft version downloads and installations
 */
public class VersionManager {
    
    private static final Logger logger = Logger.getLogger(VersionManager.class);
    private static final ObjectMapper mapper = new ObjectMapper();
    
    private static final String VERSION_MANIFEST_URL = "https://piston-meta.mojang.com/mc/game/version_manifest_v2.json";
    private static final String RESOURCES_URL = "https://resources.download.minecraft.net/";
    
    private final CloseableHttpClient httpClient;
    
    public VersionManager() {
        this.httpClient = HttpClients.createDefault();
    }
    
    /**
     * Get list of available Minecraft versions
     */
    public CompletableFuture<List<MinecraftVersion>> getAvailableVersions() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("Fetching available Minecraft versions");
                
                HttpGet get = new HttpGet(VERSION_MANIFEST_URL);
                return httpClient.execute(get, response -> {
                    if (response.getCode() == 200) {
                        JsonNode manifest = mapper.readTree(response.getEntity().getContent());
                        List<MinecraftVersion> versions = new ArrayList<>();
                        
                        for (JsonNode versionNode : manifest.get("versions")) {
                            String id = versionNode.get("id").asText();
                            String type = versionNode.get("type").asText();
                            String url = versionNode.get("url").asText();
                            String releaseTime = versionNode.get("releaseTime").asText();
                            
                            // Filter supported versions (1.8.9 - 1.21.5)
                            if (isSupportedVersion(id)) {
                                versions.add(new MinecraftVersion(id, type, url, releaseTime));
                            }
                        }
                        
                        logger.info("Found {} supported versions", versions.size());
                        return versions;
                    }
                    throw new IOException("Failed to fetch version manifest: " + response.getCode());
                });
                
            } catch (Exception e) {
                logger.error("Failed to fetch available versions", e);
                return new ArrayList<>();
            }
        });
    }
    
    /**
     * Download and install a Minecraft version
     */
    public CompletableFuture<Boolean> installVersion(String versionId, ProgressCallback callback) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("Installing Minecraft version: {}", versionId);
                callback.updateProgress("Fetching version information...", 0);
                
                // Get version manifest
                MinecraftVersion version = getVersionInfo(versionId);
                if (version == null) {
                    callback.updateProgress("Failed to get version information", 0);
                    return false;
                }
                
                // Download version JSON
                callback.updateProgress("Downloading version manifest...", 10);
                VersionManifest manifest = downloadVersionManifest(version);
                if (manifest == null) {
                    callback.updateProgress("Failed to download version manifest", 0);
                    return false;
                }
                
                // Create version directory
                Path versionDir = Paths.get(LauncherConfig.getVersionsDir(), versionId);
                Files.createDirectories(versionDir);
                
                // Download client JAR
                callback.updateProgress("Downloading client JAR...", 20);
                if (!downloadClientJar(manifest, versionDir, versionId)) {
                    callback.updateProgress("Failed to download client JAR", 0);
                    return false;
                }
                
                // Download libraries
                callback.updateProgress("Downloading libraries...", 40);
                if (!downloadLibraries(manifest, callback)) {
                    callback.updateProgress("Failed to download libraries", 0);
                    return false;
                }
                
                // Download assets
                callback.updateProgress("Downloading assets...", 70);
                if (!downloadAssets(manifest, callback)) {
                    callback.updateProgress("Failed to download assets", 0);
                    return false;
                }
                
                // Save version manifest locally
                File manifestFile = versionDir.resolve(versionId + ".json").toFile();
                mapper.writeValue(manifestFile, manifest);
                
                callback.updateProgress("Installation complete!", 100);
                logger.info("Successfully installed Minecraft version: {}", versionId);
                return true;
                
            } catch (Exception e) {
                logger.error("Failed to install version: " + versionId, e);
                callback.updateProgress("Installation failed: " + e.getMessage(), 0);
                return false;
            }
        });
    }
    
    /**
     * Check if version is already installed
     */
    public boolean isVersionInstalled(String versionId) {
        Path versionDir = Paths.get(LauncherConfig.getVersionsDir(), versionId);
        Path jarFile = versionDir.resolve(versionId + ".jar");
        Path jsonFile = versionDir.resolve(versionId + ".json");
        
        return Files.exists(jarFile) && Files.exists(jsonFile);
    }
    
    /**
     * Get installed version manifest
     */
    public VersionManifest getInstalledVersion(String versionId) {
        try {
            Path jsonFile = Paths.get(LauncherConfig.getVersionsDir(), versionId, versionId + ".json");
            if (Files.exists(jsonFile)) {
                return mapper.readValue(jsonFile.toFile(), VersionManifest.class);
            }
        } catch (IOException e) {
            logger.error("Failed to read version manifest for: " + versionId, e);
        }
        return null;
    }
    
    private boolean isSupportedVersion(String versionId) {
        // Support versions 1.8.9 through 1.21.5
        if (versionId.matches("1\\.(8|9|10|11|12|13|14|15|16|17|18|19|20|21)\\..*")) {
            return true;
        }
        // Also support snapshots and pre-releases of supported versions
        return versionId.matches("(1[89]w\\d+[a-z]|2[0-4]w\\d+[a-z]|1\\.(9|10|11|12|13|14|15|16|17|18|19|20|21)-pre\\d+)");
    }
    
    private MinecraftVersion getVersionInfo(String versionId) throws IOException {
        HttpGet get = new HttpGet(VERSION_MANIFEST_URL);
        return httpClient.execute(get, response -> {
            if (response.getCode() == 200) {
                JsonNode manifest = mapper.readTree(response.getEntity().getContent());
                for (JsonNode versionNode : manifest.get("versions")) {
                    if (versionNode.get("id").asText().equals(versionId)) {
                        return new MinecraftVersion(
                            versionNode.get("id").asText(),
                            versionNode.get("type").asText(),
                            versionNode.get("url").asText(),
                            versionNode.get("releaseTime").asText()
                        );
                    }
                }
            }
            return null;
        });
    }
    
    private VersionManifest downloadVersionManifest(MinecraftVersion version) throws IOException {
        HttpGet get = new HttpGet(version.url);
        return httpClient.execute(get, response -> {
            if (response.getCode() == 200) {
                return mapper.readValue(response.getEntity().getContent(), VersionManifest.class);
            }
            return null;
        });
    }
    
    private boolean downloadClientJar(VersionManifest manifest, Path versionDir, String versionId) {
        try {
            String jarUrl = manifest.downloads.client.url;
            Path jarFile = versionDir.resolve(versionId + ".jar");
            
            downloadFile(jarUrl, jarFile.toFile());
            return true;
        } catch (Exception e) {
            logger.error("Failed to download client JAR", e);
            return false;
        }
    }
    
    private boolean downloadLibraries(VersionManifest manifest, ProgressCallback callback) {
        try {
            int totalLibraries = manifest.libraries.size();
            int downloaded = 0;
            
            for (VersionManifest.Library library : manifest.libraries) {
                if (library.downloads != null && library.downloads.artifact != null) {
                    String libPath = library.downloads.artifact.path;
                    String libUrl = library.downloads.artifact.url;
                    
                    Path libFile = Paths.get(LauncherConfig.getLibrariesDir(), libPath);
                    Files.createDirectories(libFile.getParent());
                    
                    if (!Files.exists(libFile)) {
                        downloadFile(libUrl, libFile.toFile());
                    }
                }
                
                downloaded++;
                int progress = 40 + (downloaded * 30 / totalLibraries);
                callback.updateProgress("Downloading libraries... (" + downloaded + "/" + totalLibraries + ")", progress);
            }
            
            return true;
        } catch (Exception e) {
            logger.error("Failed to download libraries", e);
            return false;
        }
    }
    
    private boolean downloadAssets(VersionManifest manifest, ProgressCallback callback) {
        try {
            // Download asset index
            String assetIndexUrl = manifest.assetIndex.url;
            Path assetIndexFile = Paths.get(LauncherConfig.getAssetsDir(), "indexes", manifest.assetIndex.id + ".json");
            Files.createDirectories(assetIndexFile.getParent());
            
            downloadFile(assetIndexUrl, assetIndexFile.toFile());
            
            // Parse asset index and download assets
            JsonNode assetIndex = mapper.readTree(assetIndexFile.toFile());
            JsonNode objects = assetIndex.get("objects");
            
            int totalAssets = objects.size();
            int downloaded = 0;
            
            for (JsonNode asset : objects) {
                String hash = asset.get("hash").asText();
                String assetUrl = RESOURCES_URL + hash.substring(0, 2) + "/" + hash;
                
                Path assetFile = Paths.get(LauncherConfig.getAssetsDir(), "objects", hash.substring(0, 2), hash);
                Files.createDirectories(assetFile.getParent());
                
                if (!Files.exists(assetFile)) {
                    downloadFile(assetUrl, assetFile.toFile());
                }
                
                downloaded++;
                if (downloaded % 100 == 0) {
                    int progress = 70 + (downloaded * 25 / totalAssets);
                    callback.updateProgress("Downloading assets... (" + downloaded + "/" + totalAssets + ")", progress);
                }
            }
            
            return true;
        } catch (Exception e) {
            logger.error("Failed to download assets", e);
            return false;
        }
    }
    
    private void downloadFile(String url, File destination) throws IOException {
        try (InputStream in = new URL(url).openStream();
             FileOutputStream out = new FileOutputStream(destination)) {
            
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
        }
    }
    
    public void close() throws IOException {
        httpClient.close();
    }
    
    // Data classes
    public static class MinecraftVersion {
        public final String id;
        public final String type;
        public final String url;
        public final String releaseTime;
        
        public MinecraftVersion(String id, String type, String url, String releaseTime) {
            this.id = id;
            this.type = type;
            this.url = url;
            this.releaseTime = releaseTime;
        }
    }
    
    public interface ProgressCallback {
        void updateProgress(String message, int percentage);
    }
}
