package com.vexleyofficial.ravyn.client.module.modules.combat;

import com.vexleyofficial.ravyn.client.module.Module;
import com.vexleyofficial.ravyn.client.setting.NumberSetting;

/**
 * Velocity module - Reduces knockback
 */
public class Velocity extends Module {
    
    private final NumberSetting horizontal = new NumberSetting("Horizontal", 0.0, 0.0, 100.0, 5.0);
    private final NumberSetting vertical = new NumberSetting("Vertical", 0.0, 0.0, 100.0, 5.0);
    
    public Velocity() {
        super("Velocity", "Reduces knockback taken", Category.COMBAT);
        addSetting(horizontal);
        addSetting(vertical);
    }
}
