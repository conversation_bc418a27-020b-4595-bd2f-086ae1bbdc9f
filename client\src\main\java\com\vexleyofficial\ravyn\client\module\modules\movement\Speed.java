package com.vexleyofficial.ravyn.client.module.modules.movement;

import com.vexleyofficial.ravyn.client.module.Module;
import com.vexleyofficial.ravyn.client.setting.NumberSetting;

/**
 * Speed module - Increases movement speed
 */
public class Speed extends Module {
    
    private final NumberSetting speed = new NumberSetting("Speed", 2.0, 1.0, 10.0, 0.1);
    
    public Speed() {
        super("Speed", "Increases your movement speed", Category.MOVEMENT);
        addSetting(speed);
    }
}
