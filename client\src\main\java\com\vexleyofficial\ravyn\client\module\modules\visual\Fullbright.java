package com.vexleyofficial.ravyn.client.module.modules.visual;

import com.vexleyofficial.ravyn.client.module.Module;

/**
 * Fullbright module - Maximum brightness
 */
public class Fullbright extends Module {
    
    private double oldGamma;
    
    public Fullbright() {
        super("Fullbright", "Sets brightness to maximum", Category.VISUAL);
    }
    
    @Override
    public void onEnable() {
        super.onEnable();
        if (mc.options != null) {
            oldGamma = mc.options.getGamma().getValue();
            mc.options.getGamma().setValue(16.0);
        }
    }
    
    @Override
    public void onDisable() {
        super.onDisable();
        if (mc.options != null) {
            mc.options.getGamma().setValue(oldGamma);
        }
    }
}
