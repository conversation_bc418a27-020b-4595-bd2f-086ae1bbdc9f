# Ravyn Usage Guide

## Installation

### Method 1: Using the Installer (Recommended)

1. Download `RavynInstaller.exe` or `RavynInstaller.jar` from [Releases](https://github.com/vexleyofficial/Ra<PERSON>/releases)
2. Run the installer
3. Choose installation directory
4. Select installation options
5. Click "Install Ravyn"
6. Launch from desktop shortcut or start menu

### Method 2: Manual Installation

1. Download the latest launcher JAR from releases
2. Create a folder for <PERSON><PERSON> (e.g., `C:\<PERSON>vyn` or `~/<PERSON><PERSON>`)
3. Place the launcher JAR in the folder
4. Run with: `java -jar ravyn-launcher.jar`

## First Launch

### Setting Up Your Account

1. **Microsoft Account (Recommended):**
   - Click "Add Account" in the launcher
   - Select "Microsoft Account"
   - Follow the OAuth2 login process
   - Your account will be saved for future use

2. **Offline Account:**
   - Click "Add Account"
   - Select "Offline Account"
   - Enter any username
   - Note: Limited server compatibility

### Selecting Minecraft Version

1. Choose from supported versions (1.8.9 - 1.21.5)
2. The launcher will automatically download the selected version
3. First download may take several minutes

### Enabling Ravyn Client

1. Check "Use Ravyn Client (Hacked)" checkbox
2. This enables all hacked client features
3. Leave unchecked for vanilla Minecraft experience

## Using the Hacked Client

### Opening the ClickGUI

- **Default Key:** `Right Shift`
- **Alternative:** Press `Insert` key
- **In-Game:** Type `.gui` in chat

### ClickGUI Navigation

1. **Categories:** Combat, Movement, Visual, Utility, Misc
2. **Module Toggle:** Left-click on module name
3. **Settings:** Right-click on module for settings
4. **Drag Panels:** Right-click and drag category headers

### Module Categories

#### Combat Modules

| Module | Description | Key Features |
|--------|-------------|--------------|
| **KillAura** | Auto-attacks nearby entities | Range, delay, rotations, auto-block |
| **Reach** | Extends attack reach | Configurable reach distance |
| **AutoClicker** | Automatic clicking | Adjustable CPS, randomization |
| **Criticals** | Forces critical hits | Multiple bypass methods |
| **Velocity** | Reduces knockback | Horizontal/vertical reduction |

#### Movement Modules

| Module | Description | Key Features |
|--------|-------------|--------------|
| **Fly** | Creative-like flight | Speed control, bypass modes |
| **Speed** | Increases movement speed | Multiple speed modes |
| **NoFall** | Prevents fall damage | Packet-based bypass |
| **Jesus** | Walk on water | Solid water effect |
| **Sprint** | Auto-sprint | Always sprinting |

#### Visual Modules

| Module | Description | Key Features |
|--------|-------------|--------------|
| **ESP** | Entity outlines | Players, mobs, items |
| **Tracers** | Lines to entities | Customizable colors |
| **Fullbright** | Maximum brightness | Gamma override |
| **XRay** | See through blocks | Ore highlighting |
| **ChestESP** | Highlight containers | Storage detection |

#### Utility Modules

| Module | Description | Key Features |
|--------|-------------|--------------|
| **AutoMine** | Automatic mining | Block targeting |
| **Scaffold** | Auto-place blocks | Tower mode, safe walk |
| **Timer** | Game speed control | Speed multiplier |
| **FastPlace** | Faster block placing | Reduced delay |
| **AutoTool** | Auto-select tools | Best tool selection |

### Module Settings

Each module has configurable settings:

1. **Boolean Settings:** On/off toggles
2. **Number Settings:** Sliders with min/max values
3. **Mode Settings:** Dropdown selections
4. **Keybind Settings:** Custom key assignments

### Keybinds

#### Default Keybinds

- `Right Shift` - Open ClickGUI
- `R` - Toggle KillAura
- `F` - Toggle Fly
- `Insert` - Alternative GUI key

#### Setting Custom Keybinds

1. Open ClickGUI
2. Right-click on module
3. Click "Keybind"
4. Press desired key
5. Key is automatically saved

## Advanced Features

### Command System

Access via chat (prefix with `.`):

```
.help - Show all commands
.toggle <module> - Toggle module
.bind <module> <key> - Set keybind
.config save <name> - Save configuration
.config load <name> - Load configuration
.friend add <player> - Add friend
.enemy add <player> - Add enemy
```

### Configuration Management

#### Auto-Save
- Settings are automatically saved
- Keybinds are preserved
- Module states are remembered

#### Manual Backup
1. Navigate to `%APPDATA%/.ravyn/configs/`
2. Copy configuration files
3. Restore by replacing files

### Anti-Cheat Bypass

#### Built-in Bypasses
- **Watchdog (Hypixel):** Limited bypass
- **NCP (NoCheatPlus):** Good compatibility
- **AAC (AdvancedAntiCheat):** Partial bypass
- **Spartan:** Basic bypass

#### Bypass Tips
1. Use lower settings on strict servers
2. Enable "Legit Mode" when available
3. Avoid obvious hacking patterns
4. Use delays and randomization

## Troubleshooting

### Common Issues

#### Launcher Won't Start
```bash
# Check Java version
java -version

# Run with debug output
java -jar ravyn-launcher.jar --debug
```

#### Game Crashes
1. Check Minecraft version compatibility
2. Disable conflicting mods
3. Update graphics drivers
4. Allocate more RAM in launcher settings

#### Modules Not Working
1. Verify client is enabled in launcher
2. Check if server allows modifications
3. Try different bypass modes
4. Update to latest version

#### Performance Issues
1. Lower render distance
2. Disable unnecessary visual modules
3. Reduce module update rates
4. Close other applications

### Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| `AUTH_001` | Authentication failed | Re-login to account |
| `NET_002` | Network connection error | Check internet connection |
| `MOD_003` | Module initialization failed | Restart client |
| `VER_004` | Version incompatibility | Update client |

### Getting Help

1. **Check Logs:**
   - Launcher: `%APPDATA%/.ravyn/logs/`
   - Minecraft: `.minecraft/logs/`

2. **Discord Support:**
   - Join our [Discord server](https://discord.gg/ravyn)
   - Use #support channel
   - Provide error logs

3. **GitHub Issues:**
   - Report bugs on [GitHub](https://github.com/vexleyofficial/Ravyn/issues)
   - Include system information
   - Describe reproduction steps

## Best Practices

### Safe Usage

1. **Read Server Rules:** Always check if modifications are allowed
2. **Use Responsibly:** Don't ruin others' gameplay experience
3. **Stay Updated:** Keep client updated for latest bypasses
4. **Backup Worlds:** Always backup before using on important worlds

### Performance Optimization

1. **Module Management:** Only enable needed modules
2. **Settings Tuning:** Use appropriate ranges and delays
3. **Resource Allocation:** Allocate sufficient RAM
4. **Background Apps:** Close unnecessary programs

### Privacy & Security

1. **Account Safety:** Use strong passwords
2. **Token Security:** Don't share authentication tokens
3. **Server Logs:** Be aware servers may log activities
4. **Updates:** Only download from official sources

## Legal Disclaimer

⚠️ **Important Notice:**

- This software is for educational purposes only
- Users are responsible for compliance with game terms of service
- Use only on servers that allow modifications
- Developers are not responsible for account bans or consequences
- Always respect server rules and other players

## Support the Project

- ⭐ Star the repository on GitHub
- 🐛 Report bugs and issues
- 💡 Suggest new features
- 🔧 Contribute code improvements
- 💬 Join our community Discord

---

**Happy hacking with Ravyn! 🎮**
