package com.vexleyofficial.ravyn.client.mixins;

import com.vexleyofficial.ravyn.client.RavynClient;
import com.vexleyofficial.ravyn.client.event.events.RenderEvent;
import net.minecraft.client.render.Camera;
import net.minecraft.client.render.GameRenderer;
import net.minecraft.client.render.LightmapTextureManager;
import net.minecraft.client.render.WorldRenderer;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.util.math.Matrix4f;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

/**
 * Mixin for WorldRenderer to hook into world rendering
 */
@Mixin(WorldRenderer.class)
public class WorldRendererMixin {
    
    @Inject(method = "render", at = @At("TAIL"))
    private void onRender(MatrixStack matrices, float tickDelta, long limitTime, boolean renderBlockOutline, 
                         Camera camera, GameRenderer gameRenderer, LightmapTextureManager lightmapTextureManager, 
                         Matrix4f projectionMatrix, CallbackInfo ci) {
        if (<PERSON><PERSON>Client.getInstance() != null) {
            RavynClient.getInstance().getEventManager().post(
                new RenderEvent.World(matrices, tickDelta)
            );
        }
    }
}
