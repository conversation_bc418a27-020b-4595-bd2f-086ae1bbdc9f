package com.vexleyofficial.ravyn.launcher.auth;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vexleyofficial.ravyn.launcher.util.LauncherConfig;
import com.vexleyofficial.ravyn.launcher.util.Logger;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.io.entity.StringEntity;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Handles Microsoft/Mojang authentication for Minecraft accounts
 */
public class AuthenticationManager {
    
    private static final Logger logger = Logger.getLogger(AuthenticationManager.class);
    private static final ObjectMapper mapper = new ObjectMapper();
    
    // Microsoft OAuth endpoints
    private static final String MICROSOFT_AUTH_URL = "https://login.microsoftonline.com/consumers/oauth2/v2.0/token";
    private static final String XBOX_AUTH_URL = "https://user.auth.xboxlive.com/user/authenticate";
    private static final String XSTS_AUTH_URL = "https://xsts.auth.xboxlive.com/xsts/authorize";
    private static final String MINECRAFT_AUTH_URL = "https://api.minecraftservices.com/authentication/login_with_xbox";
    private static final String MINECRAFT_PROFILE_URL = "https://api.minecraftservices.com/minecraft/profile";
    
    // OAuth client details (you'll need to register your app with Microsoft)
    private static final String CLIENT_ID = "your-client-id-here";
    private static final String REDIRECT_URI = "http://localhost:8080/auth/callback";
    
    private final CloseableHttpClient httpClient;
    
    public AuthenticationManager() {
        this.httpClient = HttpClients.createDefault();
    }
    
    /**
     * Authenticate with Microsoft account
     */
    public AuthResult authenticateWithMicrosoft(String authCode) {
        try {
            logger.info("Starting Microsoft authentication");
            
            // Step 1: Get Microsoft access token
            String msAccessToken = getMicrosoftAccessToken(authCode);
            if (msAccessToken == null) {
                return AuthResult.failure("Failed to get Microsoft access token");
            }
            
            // Step 2: Authenticate with Xbox Live
            String xboxToken = authenticateWithXboxLive(msAccessToken);
            if (xboxToken == null) {
                return AuthResult.failure("Failed to authenticate with Xbox Live");
            }
            
            // Step 3: Get XSTS token
            XSTSResponse xstsResponse = getXSTSToken(xboxToken);
            if (xstsResponse == null) {
                return AuthResult.failure("Failed to get XSTS token");
            }
            
            // Step 4: Authenticate with Minecraft
            String mcAccessToken = authenticateWithMinecraft(xstsResponse);
            if (mcAccessToken == null) {
                return AuthResult.failure("Failed to authenticate with Minecraft");
            }
            
            // Step 5: Get Minecraft profile
            MinecraftProfile profile = getMinecraftProfile(mcAccessToken);
            if (profile == null) {
                return AuthResult.failure("Failed to get Minecraft profile");
            }
            
            // Create account
            LauncherConfig.Account account = new LauncherConfig.Account(
                profile.name, profile.id, false
            );
            account.setAccessToken(mcAccessToken);
            account.setExpiresAt(System.currentTimeMillis() + (3600 * 1000)); // 1 hour
            
            logger.info("Successfully authenticated user: {}", profile.name);
            return AuthResult.success(account);
            
        } catch (Exception e) {
            logger.error("Authentication failed", e);
            return AuthResult.failure("Authentication failed: " + e.getMessage());
        }
    }
    
    /**
     * Create offline account
     */
    public AuthResult createOfflineAccount(String username) {
        if (username == null || username.trim().isEmpty()) {
            return AuthResult.failure("Username cannot be empty");
        }
        
        // Generate offline UUID
        String uuid = UUID.nameUUIDFromBytes(("OfflinePlayer:" + username).getBytes()).toString();
        uuid = uuid.replaceAll("-", "");
        
        LauncherConfig.Account account = new LauncherConfig.Account(username, uuid, true);
        
        logger.info("Created offline account: {}", username);
        return AuthResult.success(account);
    }
    
    /**
     * Refresh account token if expired
     */
    public boolean refreshAccount(LauncherConfig.Account account) {
        if (account.isOffline() || !account.isExpired()) {
            return true;
        }
        
        // TODO: Implement token refresh using refresh token
        logger.warn("Token refresh not implemented yet");
        return false;
    }
    
    private String getMicrosoftAccessToken(String authCode) throws IOException {
        HttpPost post = new HttpPost(MICROSOFT_AUTH_URL);
        
        Map<String, String> params = new HashMap<>();
        params.put("client_id", CLIENT_ID);
        params.put("code", authCode);
        params.put("grant_type", "authorization_code");
        params.put("redirect_uri", REDIRECT_URI);
        
        String body = buildFormData(params);
        post.setEntity(new StringEntity(body, ContentType.APPLICATION_FORM_URLENCODED));
        
        return httpClient.execute(post, response -> {
            if (response.getCode() == 200) {
                JsonNode json = mapper.readTree(response.getEntity().getContent());
                return json.get("access_token").asText();
            }
            return null;
        });
    }
    
    private String authenticateWithXboxLive(String msAccessToken) throws IOException {
        HttpPost post = new HttpPost(XBOX_AUTH_URL);
        
        Map<String, Object> payload = new HashMap<>();
        payload.put("Properties", Map.of(
            "AuthMethod", "RPS",
            "SiteName", "user.auth.xboxlive.com",
            "RpsTicket", "d=" + msAccessToken
        ));
        payload.put("RelyingParty", "http://auth.xboxlive.com");
        payload.put("TokenType", "JWT");
        
        String body = mapper.writeValueAsString(payload);
        post.setEntity(new StringEntity(body, ContentType.APPLICATION_JSON));
        
        return httpClient.execute(post, response -> {
            if (response.getCode() == 200) {
                JsonNode json = mapper.readTree(response.getEntity().getContent());
                return json.get("Token").asText();
            }
            return null;
        });
    }
    
    private XSTSResponse getXSTSToken(String xboxToken) throws IOException {
        HttpPost post = new HttpPost(XSTS_AUTH_URL);
        
        Map<String, Object> payload = new HashMap<>();
        payload.put("Properties", Map.of(
            "SandboxId", "RETAIL",
            "UserTokens", new String[]{xboxToken}
        ));
        payload.put("RelyingParty", "rp://api.minecraftservices.com/");
        payload.put("TokenType", "JWT");
        
        String body = mapper.writeValueAsString(payload);
        post.setEntity(new StringEntity(body, ContentType.APPLICATION_JSON));
        
        return httpClient.execute(post, response -> {
            if (response.getCode() == 200) {
                JsonNode json = mapper.readTree(response.getEntity().getContent());
                String token = json.get("Token").asText();
                String userHash = json.get("DisplayClaims").get("xui").get(0).get("uhs").asText();
                return new XSTSResponse(token, userHash);
            }
            return null;
        });
    }
    
    private String authenticateWithMinecraft(XSTSResponse xstsResponse) throws IOException {
        HttpPost post = new HttpPost(MINECRAFT_AUTH_URL);
        
        Map<String, String> payload = new HashMap<>();
        payload.put("identityToken", "XBL3.0 x=" + xstsResponse.userHash + ";" + xstsResponse.token);
        
        String body = mapper.writeValueAsString(payload);
        post.setEntity(new StringEntity(body, ContentType.APPLICATION_JSON));
        
        return httpClient.execute(post, response -> {
            if (response.getCode() == 200) {
                JsonNode json = mapper.readTree(response.getEntity().getContent());
                return json.get("access_token").asText();
            }
            return null;
        });
    }
    
    private MinecraftProfile getMinecraftProfile(String accessToken) throws IOException {
        HttpGet get = new HttpGet(MINECRAFT_PROFILE_URL);
        get.setHeader("Authorization", "Bearer " + accessToken);
        
        return httpClient.execute(get, response -> {
            if (response.getCode() == 200) {
                JsonNode json = mapper.readTree(response.getEntity().getContent());
                return new MinecraftProfile(
                    json.get("id").asText(),
                    json.get("name").asText()
                );
            }
            return null;
        });
    }
    
    private String buildFormData(Map<String, String> params) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (sb.length() > 0) sb.append("&");
            sb.append(entry.getKey()).append("=").append(entry.getValue());
        }
        return sb.toString();
    }
    
    public void close() throws IOException {
        httpClient.close();
    }
    
    // Helper classes
    private static class XSTSResponse {
        final String token;
        final String userHash;
        
        XSTSResponse(String token, String userHash) {
            this.token = token;
            this.userHash = userHash;
        }
    }
    
    private static class MinecraftProfile {
        final String id;
        final String name;
        
        MinecraftProfile(String id, String name) {
            this.id = id;
            this.name = name;
        }
    }
    
    public static class AuthResult {
        private final boolean success;
        private final String error;
        private final LauncherConfig.Account account;
        
        private AuthResult(boolean success, String error, LauncherConfig.Account account) {
            this.success = success;
            this.error = error;
            this.account = account;
        }
        
        public static AuthResult success(LauncherConfig.Account account) {
            return new AuthResult(true, null, account);
        }
        
        public static AuthResult failure(String error) {
            return new AuthResult(false, error, null);
        }
        
        public boolean isSuccess() { return success; }
        public String getError() { return error; }
        public LauncherConfig.Account getAccount() { return account; }
    }
}
