package com.vexleyofficial.ravyn.installer;

import javafx.application.Application;
import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.layout.*;
import javafx.scene.paint.Color;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import javafx.stage.DirectoryChooser;
import javafx.stage.Stage;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.CompletableFuture;

/**
 * Ravyn Installer - Installs the Ravyn Launcher and Client
 */
public class RavynInstaller extends Application {
    
    private static final String APP_NAME = "Ravyn Installer";
    private static final String VERSION = "1.0.0";
    
    private Stage primaryStage;
    private TextField installPathField;
    private ProgressBar progressBar;
    private Label statusLabel;
    private Button installButton;
    private CheckBox createShortcutCheckBox;
    private CheckBox addToPathCheckBox;
    
    private String defaultInstallPath;
    
    public static void main(String[] args) {
        launch(args);
    }
    
    @Override
    public void start(Stage primaryStage) throws Exception {
        this.primaryStage = primaryStage;
        
        // Set default install path
        String userHome = System.getProperty("user.home");
        defaultInstallPath = userHome + File.separator + "Ravyn";
        
        initializeUI();
    }
    
    private void initializeUI() {
        primaryStage.setTitle(APP_NAME + " v" + VERSION);
        primaryStage.setResizable(false);
        
        // Main container
        VBox mainContainer = new VBox(20);
        mainContainer.setPadding(new Insets(30));
        mainContainer.setAlignment(Pos.CENTER);
        
        // Header
        Label titleLabel = new Label("Ravyn Launcher & Client Installer");
        titleLabel.setFont(Font.font("Arial", FontWeight.BOLD, 24));
        titleLabel.setTextFill(Color.DARKBLUE);
        
        Label subtitleLabel = new Label("Install the ultimate Minecraft hacked client experience");
        subtitleLabel.setFont(Font.font("Arial", 14));
        subtitleLabel.setTextFill(Color.GRAY);
        
        // Installation path section
        VBox pathSection = new VBox(10);
        pathSection.setAlignment(Pos.CENTER_LEFT);
        
        Label pathLabel = new Label("Installation Directory:");
        pathLabel.setFont(Font.font("Arial", FontWeight.BOLD, 14));
        
        HBox pathBox = new HBox(10);
        pathBox.setAlignment(Pos.CENTER_LEFT);
        
        installPathField = new TextField(defaultInstallPath);
        installPathField.setPrefWidth(400);
        
        Button browseButton = new Button("Browse...");
        browseButton.setOnAction(e -> browseInstallPath());
        
        pathBox.getChildren().addAll(installPathField, browseButton);
        pathSection.getChildren().addAll(pathLabel, pathBox);
        
        // Options section
        VBox optionsSection = new VBox(10);
        optionsSection.setAlignment(Pos.CENTER_LEFT);
        
        Label optionsLabel = new Label("Installation Options:");
        optionsLabel.setFont(Font.font("Arial", FontWeight.BOLD, 14));
        
        createShortcutCheckBox = new CheckBox("Create desktop shortcut");
        createShortcutCheckBox.setSelected(true);
        
        addToPathCheckBox = new CheckBox("Add to system PATH");
        addToPathCheckBox.setSelected(false);
        
        optionsSection.getChildren().addAll(optionsLabel, createShortcutCheckBox, addToPathCheckBox);
        
        // Progress section
        VBox progressSection = new VBox(10);
        progressSection.setAlignment(Pos.CENTER);
        
        progressBar = new ProgressBar(0);
        progressBar.setPrefWidth(400);
        progressBar.setVisible(false);
        
        statusLabel = new Label("Ready to install");
        statusLabel.setFont(Font.font("Arial", 12));
        
        progressSection.getChildren().addAll(progressBar, statusLabel);
        
        // Buttons section
        HBox buttonBox = new HBox(15);
        buttonBox.setAlignment(Pos.CENTER);
        
        installButton = new Button("Install Ravyn");
        installButton.setPrefWidth(120);
        installButton.setPrefHeight(40);
        installButton.setStyle("-fx-background-color: #4CAF50; -fx-text-fill: white; -fx-font-weight: bold;");
        installButton.setOnAction(e -> startInstallation());
        
        Button cancelButton = new Button("Cancel");
        cancelButton.setPrefWidth(120);
        cancelButton.setPrefHeight(40);
        cancelButton.setOnAction(e -> Platform.exit());
        
        buttonBox.getChildren().addAll(installButton, cancelButton);
        
        // Features section
        VBox featuresSection = new VBox(10);
        featuresSection.setAlignment(Pos.CENTER_LEFT);
        
        Label featuresLabel = new Label("What you'll get:");
        featuresLabel.setFont(Font.font("Arial", FontWeight.BOLD, 14));
        
        VBox featuresList = new VBox(5);
        featuresList.getChildren().addAll(
            new Label("✓ Custom Minecraft Launcher with modern UI"),
            new Label("✓ Advanced hacked client with 25+ modules"),
            new Label("✓ Support for Minecraft versions 1.8.9 - 1.21.5"),
            new Label("✓ Anti-cheat bypass capabilities"),
            new Label("✓ Regular updates and new features"),
            new Label("✓ Easy-to-use ClickGUI interface")
        );
        
        featuresSection.getChildren().addAll(featuresLabel, featuresList);
        
        // Add all sections to main container
        mainContainer.getChildren().addAll(
            titleLabel,
            subtitleLabel,
            new Separator(),
            pathSection,
            optionsSection,
            featuresSection,
            new Separator(),
            progressSection,
            buttonBox
        );
        
        // Create scene
        Scene scene = new Scene(mainContainer, 600, 700);
        primaryStage.setScene(scene);
        
        // Set application icon
        try {
            primaryStage.getIcons().add(new Image(getClass().getResourceAsStream("/icon.png")));
        } catch (Exception e) {
            // Icon not found, continue without it
        }
        
        primaryStage.show();
        primaryStage.centerOnScreen();
    }
    
    private void browseInstallPath() {
        DirectoryChooser directoryChooser = new DirectoryChooser();
        directoryChooser.setTitle("Select Installation Directory");
        directoryChooser.setInitialDirectory(new File(System.getProperty("user.home")));
        
        File selectedDirectory = directoryChooser.showDialog(primaryStage);
        if (selectedDirectory != null) {
            installPathField.setText(selectedDirectory.getAbsolutePath() + File.separator + "Ravyn");
        }
    }
    
    private void startInstallation() {
        String installPath = installPathField.getText();
        
        if (installPath.trim().isEmpty()) {
            showError("Please select an installation directory");
            return;
        }
        
        // Disable UI during installation
        installButton.setDisabled(true);
        progressBar.setVisible(true);
        
        // Start installation in background
        CompletableFuture.runAsync(() -> performInstallation(installPath))
            .thenRun(() -> Platform.runLater(this::installationComplete))
            .exceptionally(throwable -> {
                Platform.runLater(() -> {
                    showError("Installation failed: " + throwable.getMessage());
                    installButton.setDisabled(false);
                    progressBar.setVisible(false);
                });
                return null;
            });
    }
    
    private void performInstallation(String installPath) {
        try {
            updateStatus("Creating installation directory...", 10);
            Path installDir = Paths.get(installPath);
            Files.createDirectories(installDir);
            
            updateStatus("Downloading Ravyn Launcher...", 30);
            // TODO: Download launcher JAR from GitHub releases
            Thread.sleep(2000); // Simulate download
            
            updateStatus("Downloading Ravyn Client...", 50);
            // TODO: Download client JAR from GitHub releases
            Thread.sleep(2000); // Simulate download
            
            updateStatus("Installing components...", 70);
            // TODO: Extract and install components
            Thread.sleep(1000);
            
            if (createShortcutCheckBox.isSelected()) {
                updateStatus("Creating desktop shortcut...", 85);
                createDesktopShortcut(installPath);
            }
            
            if (addToPathCheckBox.isSelected()) {
                updateStatus("Adding to system PATH...", 95);
                // TODO: Add to system PATH
            }
            
            updateStatus("Installation complete!", 100);
            
        } catch (Exception e) {
            throw new RuntimeException("Installation failed", e);
        }
    }
    
    private void createDesktopShortcut(String installPath) {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            String desktop = System.getProperty("user.home") + File.separator + "Desktop";
            
            if (os.contains("windows")) {
                // Create Windows shortcut (.lnk file)
                // This would require additional libraries or native code
                // For now, just create a batch file
                String batchContent = "@echo off\n" +
                    "cd /d \"" + installPath + "\"\n" +
                    "java -jar ravyn-launcher.jar\n";
                
                Files.write(Paths.get(desktop, "Ravyn Launcher.bat"), batchContent.getBytes());
            } else if (os.contains("linux")) {
                // Create Linux desktop entry
                String desktopEntry = "[Desktop Entry]\n" +
                    "Version=1.0\n" +
                    "Type=Application\n" +
                    "Name=Ravyn Launcher\n" +
                    "Comment=Minecraft Hacked Client Launcher\n" +
                    "Exec=java -jar \"" + installPath + "/ravyn-launcher.jar\"\n" +
                    "Path=" + installPath + "\n" +
                    "Terminal=false\n" +
                    "Categories=Game;\n";
                
                Files.write(Paths.get(desktop, "ravyn-launcher.desktop"), desktopEntry.getBytes());
            }
        } catch (IOException e) {
            // Shortcut creation failed, but don't fail the entire installation
            System.err.println("Failed to create desktop shortcut: " + e.getMessage());
        }
    }
    
    private void updateStatus(String message, int progress) {
        Platform.runLater(() -> {
            statusLabel.setText(message);
            progressBar.setProgress(progress / 100.0);
        });
    }
    
    private void installationComplete() {
        Alert alert = new Alert(Alert.AlertType.INFORMATION);
        alert.setTitle("Installation Complete");
        alert.setHeaderText("Ravyn has been installed successfully!");
        alert.setContentText("You can now launch Ravyn from your desktop or start menu.\n\n" +
            "Thank you for choosing Ravyn!");
        
        alert.showAndWait();
        Platform.exit();
    }
    
    private void showError(String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle("Installation Error");
        alert.setHeaderText("Installation Failed");
        alert.setContentText(message);
        alert.showAndWait();
    }
}
