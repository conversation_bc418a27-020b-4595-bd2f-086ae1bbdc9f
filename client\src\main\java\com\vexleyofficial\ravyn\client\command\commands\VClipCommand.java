package com.vexleyofficial.ravyn.client.command.commands;

import com.vexleyofficial.ravyn.client.command.Command;

/**
 * VClip command - Vertical teleportation
 */
public class VClipCommand extends Command {
    
    public VClipCommand() {
        super("vclip", "Teleports vertically", ".vclip <distance>", "vc");
    }
    
    @Override
    public void execute(String[] args) throws Exception {
        if (args.length != 1) {
            sendUsage();
            return;
        }
        
        if (mc.player == null) {
            sendError("You must be in-game to use this command.");
            return;
        }
        
        try {
            double distance = Double.parseDouble(args[0]);
            
            double newY = mc.player.getY() + distance;
            mc.player.setPosition(mc.player.getX(), newY, mc.player.getZ());
            
            sendSuccess("Teleported §f" + distance + " §ablocks " + 
                       (distance > 0 ? "up" : "down") + ".");
            
        } catch (NumberFormatException e) {
            sendError("Invalid distance. Please enter a number.");
        }
    }
}
