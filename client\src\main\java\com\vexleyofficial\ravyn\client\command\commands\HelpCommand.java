package com.vexleyofficial.ravyn.client.command.commands;

import com.vexleyofficial.ravyn.client.RavynClient;
import com.vexleyofficial.ravyn.client.command.Command;
import com.vexleyofficial.ravyn.client.command.CommandManager;

/**
 * Help command - Shows available commands
 */
public class HelpCommand extends Command {
    
    public HelpCommand() {
        super("help", "Shows available commands", ".help [command]", "h", "?");
    }
    
    @Override
    public void execute(String[] args) throws Exception {
        CommandManager cmdManager = RavynClient.getInstance().getCommandManager();
        
        if (args.length == 0) {
            // Show all commands
            sendMessage("§7Available commands:");
            
            for (Command command : cmdManager.getCommands()) {
                sendMessage("§f" + CommandManager.getPrefix() + command.getName() + 
                           " §7- " + command.getDescription());
            }
            
            sendMessage("§7Type §f" + CommandManager.getPrefix() + "help <command> §7for detailed usage.");
            
        } else {
            // Show specific command help
            String commandName = args[0];
            Command command = cmdManager.getCommand(commandName);
            
            if (command == null) {
                sendError("Command '" + commandName + "' not found.");
                return;
            }
            
            sendMessage("§7Command: §f" + command.getName());
            sendMessage("§7Description: §f" + command.getDescription());
            sendMessage("§7Usage: §f" + command.getUsage());
            
            if (!command.getAliases().isEmpty()) {
                sendMessage("§7Aliases: §f" + String.join(", ", command.getAliases()));
            }
        }
    }
}
