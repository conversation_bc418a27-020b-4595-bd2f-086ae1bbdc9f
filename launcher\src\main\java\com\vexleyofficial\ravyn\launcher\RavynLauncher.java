package com.vexleyofficial.ravyn.launcher;

import com.vexleyofficial.ravyn.launcher.ui.LauncherController;
import com.vexleyofficial.ravyn.launcher.util.LauncherConfig;
import com.vexleyofficial.ravyn.launcher.util.Logger;
import javafx.application.Application;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.scene.image.Image;
import javafx.stage.Stage;
import javafx.stage.StageStyle;

import java.io.IOException;
import java.util.Objects;

/**
 * Ravyn Launcher - Custom Minecraft Launcher with Hacked Client Support
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class RavynLauncher extends Application {
    
    private static final String APP_NAME = "Ravyn Launcher";
    private static final String VERSION = "1.0.0";
    private static final Logger logger = Logger.getLogger(RavynLauncher.class);
    
    private Stage primaryStage;
    private LauncherController controller;
    
    public static void main(String[] args) {
        logger.info("Starting {} v{}", APP_NAME, VERSION);
        logger.info("Java Version: {}", System.getProperty("java.version"));
        logger.info("OS: {} {}", System.getProperty("os.name"), System.getProperty("os.version"));
        
        // Initialize launcher configuration
        LauncherConfig.initialize();
        
        // Launch JavaFX application
        launch(args);
    }
    
    @Override
    public void start(Stage primaryStage) throws Exception {
        this.primaryStage = primaryStage;
        
        try {
            initializeUI();
            logger.info("Launcher UI initialized successfully");
        } catch (Exception e) {
            logger.error("Failed to initialize launcher UI", e);
            throw e;
        }
    }
    
    private void initializeUI() throws IOException {
        // Load FXML
        FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/launcher.fxml"));
        Scene scene = new Scene(loader.load());
        
        // Get controller
        controller = loader.getController();
        controller.setLauncher(this);
        
        // Configure stage
        primaryStage.setTitle(APP_NAME + " v" + VERSION);
        primaryStage.setScene(scene);
        primaryStage.setResizable(false);
        primaryStage.initStyle(StageStyle.DECORATED);
        
        // Set application icon
        try {
            Image icon = new Image(Objects.requireNonNull(
                getClass().getResourceAsStream("/images/ravyn-icon.png")
            ));
            primaryStage.getIcons().add(icon);
        } catch (Exception e) {
            logger.warn("Could not load application icon", e);
        }
        
        // Load CSS theme
        scene.getStylesheets().add(
            Objects.requireNonNull(getClass().getResource("/css/launcher.css")).toExternalForm()
        );
        
        // Show stage
        primaryStage.show();
        
        // Center on screen
        primaryStage.centerOnScreen();
    }
    
    @Override
    public void stop() throws Exception {
        logger.info("Shutting down launcher");
        
        // Save configuration
        LauncherConfig.save();
        
        // Cleanup resources
        if (controller != null) {
            controller.cleanup();
        }
        
        super.stop();
    }
    
    public Stage getPrimaryStage() {
        return primaryStage;
    }
    
    public static String getVersion() {
        return VERSION;
    }
    
    public static String getAppName() {
        return APP_NAME;
    }
}
