package com.vexleyofficial.ravyn.client.module;

import com.vexleyofficial.ravyn.client.RavynClient;
import com.vexleyofficial.ravyn.client.setting.Setting;
import com.vexleyofficial.ravyn.client.util.Logger;
import net.minecraft.client.MinecraftClient;

import java.util.ArrayList;
import java.util.List;

/**
 * Base class for all client modules
 */
public abstract class Module {
    
    protected static final MinecraftClient mc = MinecraftClient.getInstance();
    protected static final Logger logger = Logger.getLogger(Module.class);
    
    private final String name;
    private final String description;
    private final Category category;
    private final int defaultKey;
    
    private boolean enabled = false;
    private int key = 0;
    private final List<Setting<?>> settings = new ArrayList<>();
    
    public Module(String name, String description, Category category, int defaultKey) {
        this.name = name;
        this.description = description;
        this.category = category;
        this.defaultKey = defaultKey;
        this.key = defaultKey;
    }
    
    public Module(String name, String description, Category category) {
        this(name, description, category, 0);
    }
    
    /**
     * Called when the module is enabled
     */
    public void onEnable() {
        RavynClient.getInstance().getEventManager().register(this);
        logger.debug("Enabled module: {}", name);
    }
    
    /**
     * Called when the module is disabled
     */
    public void onDisable() {
        RavynClient.getInstance().getEventManager().unregister(this);
        logger.debug("Disabled module: {}", name);
    }
    
    /**
     * Called every tick when the module is enabled
     */
    public void onTick() {
        // Override in subclasses
    }
    
    /**
     * Toggle the module on/off
     */
    public void toggle() {
        setEnabled(!enabled);
    }
    
    /**
     * Set the enabled state
     */
    public void setEnabled(boolean enabled) {
        if (this.enabled == enabled) return;
        
        this.enabled = enabled;
        
        if (enabled) {
            onEnable();
        } else {
            onDisable();
        }
    }
    
    /**
     * Add a setting to this module
     */
    protected void addSetting(Setting<?> setting) {
        settings.add(setting);
        RavynClient.getInstance().getSettingManager().addSetting(setting);
    }
    
    // Getters
    public String getName() { return name; }
    public String getDescription() { return description; }
    public Category getCategory() { return category; }
    public boolean isEnabled() { return enabled; }
    public int getKey() { return key; }
    public void setKey(int key) { this.key = key; }
    public List<Setting<?>> getSettings() { return settings; }
    
    /**
     * Module categories
     */
    public enum Category {
        COMBAT("Combat"),
        MOVEMENT("Movement"),
        VISUAL("Visual"),
        UTILITY("Utility"),
        MISC("Misc");
        
        private final String name;
        
        Category(String name) {
            this.name = name;
        }
        
        public String getName() {
            return name;
        }
    }
}
