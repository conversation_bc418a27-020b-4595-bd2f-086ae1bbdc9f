# Maven Installation Script for Windows
# Run this script as Administrator

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Maven Installation Script" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "ERROR: This script must be run as Administrator" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Maven version and URLs
$mavenVersion = "3.9.5"
$mavenUrl = "https://archive.apache.org/dist/maven/maven-3/$mavenVersion/binaries/apache-maven-$mavenVersion-bin.zip"
$installPath = "C:\Program Files\Apache\maven"
$tempPath = "$env:TEMP\apache-maven-$mavenVersion-bin.zip"

Write-Host "[1/5] Downloading Maven $mavenVersion..." -ForegroundColor Green

try {
    # Download Maven
    Invoke-WebRequest -Uri $mavenUrl -OutFile $tempPath -UseBasicParsing
    Write-Host "✓ Download completed" -ForegroundColor Green
} catch {
    Write-Host "✗ Download failed: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "[2/5] Creating installation directory..." -ForegroundColor Green

# Create installation directory
try {
    New-Item -ItemType Directory -Force -Path "C:\Program Files\Apache" | Out-Null
    Write-Host "✓ Directory created: C:\Program Files\Apache" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to create directory: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "[3/5] Extracting Maven..." -ForegroundColor Green

try {
    # Extract Maven
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::ExtractToDirectory($tempPath, "C:\Program Files\Apache\")
    
    # Rename extracted folder to 'maven'
    $extractedPath = "C:\Program Files\Apache\apache-maven-$mavenVersion"
    if (Test-Path $installPath) {
        Remove-Item $installPath -Recurse -Force
    }
    Rename-Item $extractedPath $installPath
    
    Write-Host "✓ Maven extracted to: $installPath" -ForegroundColor Green
} catch {
    Write-Host "✗ Extraction failed: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "[4/5] Setting up environment variables..." -ForegroundColor Green

try {
    # Set MAVEN_HOME
    [Environment]::SetEnvironmentVariable("MAVEN_HOME", $installPath, [EnvironmentVariableTarget]::Machine)
    
    # Add Maven to PATH
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", [EnvironmentVariableTarget]::Machine)
    $mavenBinPath = "$installPath\bin"
    
    if ($currentPath -notlike "*$mavenBinPath*") {
        $newPath = "$currentPath;$mavenBinPath"
        [Environment]::SetEnvironmentVariable("PATH", $newPath, [EnvironmentVariableTarget]::Machine)
        Write-Host "✓ Added Maven to PATH" -ForegroundColor Green
    } else {
        Write-Host "✓ Maven already in PATH" -ForegroundColor Green
    }
    
    # Update current session PATH
    $env:PATH += ";$mavenBinPath"
    $env:MAVEN_HOME = $installPath
    
} catch {
    Write-Host "✗ Failed to set environment variables: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "[5/5] Cleaning up..." -ForegroundColor Green

try {
    # Remove temporary file
    Remove-Item $tempPath -Force
    Write-Host "✓ Temporary files cleaned up" -ForegroundColor Green
} catch {
    Write-Host "⚠ Warning: Could not remove temporary file: $tempPath" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    MAVEN INSTALLATION COMPLETE!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Maven has been installed to: $installPath" -ForegroundColor Green
Write-Host "MAVEN_HOME: $installPath" -ForegroundColor Green
Write-Host "Added to PATH: $installPath\bin" -ForegroundColor Green
Write-Host ""
Write-Host "IMPORTANT: You need to restart your command prompt/PowerShell" -ForegroundColor Yellow
Write-Host "for the PATH changes to take effect." -ForegroundColor Yellow
Write-Host ""
Write-Host "To verify installation, open a new command prompt and run:" -ForegroundColor Cyan
Write-Host "mvn -version" -ForegroundColor White
Write-Host ""
Write-Host "Then you can run the Ravyn build script:" -ForegroundColor Cyan
Write-Host ".\build.bat" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to exit"
