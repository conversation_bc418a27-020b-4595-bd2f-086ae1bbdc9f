package com.vexleyofficial.ravyn.client.mixins;

import com.vexleyofficial.ravyn.client.RavynClient;
import com.vexleyofficial.ravyn.client.module.modules.combat.Reach;
import com.vexleyofficial.ravyn.client.module.modules.utility.FastPlace;
import net.minecraft.client.network.ClientPlayerInteractionManager;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

/**
 * Mixin for ClientPlayerInteractionManager to modify interaction behavior
 */
@Mixin(ClientPlayerInteractionManager.class)
public class ClientPlayerInteractionManagerMixin {
    
    @Shadow private int blockBreakingCooldown;
    
    @Inject(method = "getReachDistance", at = @At("HEAD"), cancellable = true)
    private void onGetReachDistance(CallbackInfoReturnable<Float> cir) {
        if (RavynClient.getInstance() != null) {
            Reach reach = RavynClient.getInstance().getModuleManager().getModule(Reach.class);
            if (reach != null && reach.isEnabled()) {
                cir.setReturnValue((float) reach.getReach());
            }
        }
    }
    
    @Inject(method = "updateBlockBreakingProgress", at = @At("HEAD"))
    private void onUpdateBlockBreakingProgress(CallbackInfoReturnable<Boolean> cir) {
        if (RavynClient.getInstance() != null) {
            FastPlace fastPlace = RavynClient.getInstance().getModuleManager().getModule(FastPlace.class);
            if (fastPlace != null && fastPlace.isEnabled()) {
                blockBreakingCooldown = fastPlace.getDelay();
            }
        }
    }
}
