package com.vexleyofficial.ravyn.client.module.modules.combat;

import com.vexleyofficial.ravyn.client.event.EventHandler;
import com.vexleyofficial.ravyn.client.event.events.UpdateEvent;
import com.vexleyofficial.ravyn.client.module.Module;
import com.vexleyofficial.ravyn.client.setting.BooleanSetting;
import com.vexleyofficial.ravyn.client.setting.NumberSetting;
import net.minecraft.entity.Entity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.mob.Monster;
import net.minecraft.entity.passive.AnimalEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.util.Hand;
import net.minecraft.util.math.Box;
import org.lwjgl.glfw.GLFW;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * KillAura module - Automatically attacks nearby entities
 */
public class KillAura extends Module {
    
    private final NumberSetting range = new NumberSetting("Range", 4.0, 1.0, 6.0, 0.1);
    private final NumberSetting delay = new NumberSetting("Delay", 100, 0, 1000, 10);
    private final BooleanSetting players = new BooleanSetting("Players", true);
    private final BooleanSetting mobs = new BooleanSetting("Mobs", true);
    private final BooleanSetting animals = new BooleanSetting("Animals", false);
    private final BooleanSetting autoBlock = new BooleanSetting("Auto Block", true);
    private final BooleanSetting rotations = new BooleanSetting("Rotations", true);
    
    private long lastAttack = 0;
    private LivingEntity target = null;
    
    public KillAura() {
        super("KillAura", "Automatically attacks nearby entities", Category.COMBAT, GLFW.GLFW_KEY_R);
        
        addSetting(range);
        addSetting(delay);
        addSetting(players);
        addSetting(mobs);
        addSetting(animals);
        addSetting(autoBlock);
        addSetting(rotations);
    }
    
    @Override
    public void onTick() {
        if (mc.player == null || mc.world == null) return;
        
        // Find target
        target = findTarget();
        
        if (target != null) {
            // Rotate to target
            if (rotations.getValue()) {
                rotateToTarget(target);
            }
            
            // Attack target
            if (System.currentTimeMillis() - lastAttack >= delay.getValue()) {
                attackTarget(target);
                lastAttack = System.currentTimeMillis();
            }
        }
    }
    
    @EventHandler
    public void onUpdate(UpdateEvent.Pre event) {
        // Additional update logic if needed
    }
    
    private LivingEntity findTarget() {
        if (mc.world == null || mc.player == null) return null;
        
        Box searchBox = mc.player.getBoundingBox().expand(range.getValue());
        List<Entity> entities = mc.world.getOtherEntities(mc.player, searchBox);
        
        return entities.stream()
            .filter(entity -> entity instanceof LivingEntity)
            .map(entity -> (LivingEntity) entity)
            .filter(this::isValidTarget)
            .min(Comparator.comparingDouble(entity -> mc.player.distanceTo(entity)))
            .orElse(null);
    }
    
    private boolean isValidTarget(LivingEntity entity) {
        if (entity == mc.player) return false;
        if (entity.isDead() || entity.getHealth() <= 0) return false;
        if (mc.player.distanceTo(entity) > range.getValue()) return false;
        
        // Check entity types
        if (entity instanceof PlayerEntity && !players.getValue()) return false;
        if (entity instanceof Monster && !mobs.getValue()) return false;
        if (entity instanceof AnimalEntity && !animals.getValue()) return false;
        
        return true;
    }
    
    private void rotateToTarget(LivingEntity target) {
        if (mc.player == null) return;
        
        double deltaX = target.getX() - mc.player.getX();
        double deltaY = target.getY() + target.getEyeHeight(target.getPose()) - mc.player.getEyeY();
        double deltaZ = target.getZ() - mc.player.getZ();
        
        double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
        
        float yaw = (float) (Math.atan2(deltaZ, deltaX) * 180.0 / Math.PI) - 90.0f;
        float pitch = (float) -(Math.atan2(deltaY, distance) * 180.0 / Math.PI);
        
        mc.player.setYaw(yaw);
        mc.player.setPitch(pitch);
    }
    
    private void attackTarget(LivingEntity target) {
        if (mc.player == null || mc.interactionManager == null) return;
        
        // Auto block before attack
        if (autoBlock.getValue() && mc.player.getMainHandStack().getItem().toString().contains("sword")) {
            mc.interactionManager.interactItem(mc.player, Hand.OFF_HAND);
        }
        
        // Attack the target
        mc.interactionManager.attackEntity(mc.player, target);
        mc.player.swingHand(Hand.MAIN_HAND);
        
        // Auto block after attack
        if (autoBlock.getValue() && mc.player.getMainHandStack().getItem().toString().contains("sword")) {
            mc.interactionManager.interactItem(mc.player, Hand.MAIN_HAND);
        }
    }
    
    public LivingEntity getTarget() {
        return target;
    }
}
