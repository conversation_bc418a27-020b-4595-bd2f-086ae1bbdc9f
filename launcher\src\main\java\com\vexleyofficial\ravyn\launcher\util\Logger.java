package com.vexleyofficial.ravyn.launcher.util;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Simple logging utility for the launcher
 */
public class Logger {
    
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final ConcurrentHashMap<String, Logger> loggers = new ConcurrentHashMap<>();
    
    private final String name;
    
    private Logger(String name) {
        this.name = name;
    }
    
    public static Logger getLogger(Class<?> clazz) {
        return getLogger(clazz.getSimpleName());
    }
    
    public static Logger getLogger(String name) {
        return loggers.computeIfAbsent(name, Logger::new);
    }
    
    public void debug(String message, Object... args) {
        log(Level.DEBUG, message, args);
    }
    
    public void info(String message, Object... args) {
        log(Level.INFO, message, args);
    }
    
    public void warn(String message, Object... args) {
        log(Level.WARN, message, args);
    }
    
    public void warn(String message, Throwable throwable, Object... args) {
        log(Level.WARN, message, throwable, args);
    }
    
    public void error(String message, Object... args) {
        log(Level.ERROR, message, args);
    }
    
    public void error(String message, Throwable throwable, Object... args) {
        log(Level.ERROR, message, throwable, args);
    }
    
    private void log(Level level, String message, Object... args) {
        log(level, message, null, args);
    }
    
    private void log(Level level, String message, Throwable throwable, Object... args) {
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
        String formattedMessage = args.length > 0 ? String.format(message, args) : message;
        
        StringBuilder logEntry = new StringBuilder();
        logEntry.append("[").append(timestamp).append("] ");
        logEntry.append("[").append(level.name()).append("] ");
        logEntry.append("[").append(name).append("] ");
        logEntry.append(formattedMessage);
        
        if (throwable != null) {
            logEntry.append("\n").append(getStackTrace(throwable));
        }
        
        // Output to console
        if (level == Level.ERROR || level == Level.WARN) {
            System.err.println(logEntry.toString());
        } else {
            System.out.println(logEntry.toString());
        }
        
        // TODO: Add file logging if needed
    }
    
    private String getStackTrace(Throwable throwable) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        throwable.printStackTrace(pw);
        return sw.toString();
    }
    
    private enum Level {
        DEBUG, INFO, WARN, ERROR
    }
}
