package com.vexleyofficial.ravyn.client.module.modules.utility;

import com.vexleyofficial.ravyn.client.module.Module;
import com.vexleyofficial.ravyn.client.setting.NumberSetting;

/**
 * Timer module - Changes game speed
 */
public class Timer extends Module {
    
    private final NumberSetting speed = new NumberSetting("Speed", 2.0, 0.1, 10.0, 0.1);
    
    public Timer() {
        super("Timer", "Changes the speed of the game", Category.UTILITY);
        addSetting(speed);
    }
    
    public float getSpeed() {
        return isEnabled() ? speed.getValueFloat() : 1.0f;
    }
}
