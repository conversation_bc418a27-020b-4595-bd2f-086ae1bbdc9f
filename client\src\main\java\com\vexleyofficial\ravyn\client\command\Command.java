package com.vexleyofficial.ravyn.client.command;

import com.vexleyofficial.ravyn.client.RavynClient;
import net.minecraft.client.MinecraftClient;

import java.util.ArrayList;
import java.util.List;

/**
 * Base class for all client commands
 */
public abstract class Command {
    
    protected static final MinecraftClient mc = MinecraftClient.getInstance();
    
    private final String name;
    private final String description;
    private final String usage;
    private final List<String> aliases;
    
    public Command(String name, String description, String usage, String... aliases) {
        this.name = name;
        this.description = description;
        this.usage = usage;
        this.aliases = new ArrayList<>();
        for (String alias : aliases) {
            this.aliases.add(alias.toLowerCase());
        }
    }
    
    public Command(String name, String description, String usage) {
        this(name, description, usage, new String[0]);
    }
    
    /**
     * Execute the command
     */
    public abstract void execute(String[] args) throws Exception;
    
    /**
     * Send a message to the player
     */
    protected void sendMessage(String message) {
        RavynClient.getInstance().getCommandManager().sendMessage(message);
    }
    
    /**
     * Send an error message
     */
    protected void sendError(String message) {
        sendMessage("§c" + message);
    }
    
    /**
     * Send a success message
     */
    protected void sendSuccess(String message) {
        sendMessage("§a" + message);
    }
    
    /**
     * Send usage information
     */
    protected void sendUsage() {
        sendMessage("§7Usage: §f" + usage);
    }
    
    // Getters
    public String getName() { return name; }
    public String getDescription() { return description; }
    public String getUsage() { return usage; }
    public List<String> getAliases() { return aliases; }
}
