package com.vexleyofficial.ravyn.client.module.modules.movement;

import com.vexleyofficial.ravyn.client.module.Module;
import com.vexleyofficial.ravyn.client.setting.NumberSetting;
import org.lwjgl.glfw.GLFW;

/**
 * Fly module - Allows creative-like flight
 */
public class Fly extends Module {
    
    private final NumberSetting speed = new NumberSetting("Speed", 1.0, 0.1, 10.0, 0.1);
    
    public Fly() {
        super("Fly", "Allows you to fly like in creative mode", Category.MOVEMENT, GLFW.GLFW_KEY_F);
        addSetting(speed);
    }
    
    @Override
    public void onTick() {
        if (mc.player == null) return;
        
        mc.player.getAbilities().flying = true;
        mc.player.getAbilities().setFlySpeed(speed.getValueFloat() * 0.05f);
    }
    
    @Override
    public void onDisable() {
        super.onDisable();
        if (mc.player != null) {
            mc.player.getAbilities().flying = false;
            mc.player.getAbilities().setFlySpeed(0.05f);
        }
    }
}
