package com.vexleyofficial.ravyn.client.mixins;

import com.vexleyofficial.ravyn.client.RavynClient;
import com.vexleyofficial.ravyn.client.event.events.UpdateEvent;
import com.vexleyofficial.ravyn.client.module.modules.combat.Velocity;
import com.vexleyofficial.ravyn.client.module.modules.movement.NoFall;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.ModifyVariable;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

/**
 * Mixin for ClientPlayerEntity to hook into player events
 */
@Mixin(ClientPlayerEntity.class)
public class ClientPlayerEntityMixin {
    
    @Inject(method = "tick", at = @At("HEAD"))
    private void onTickPre(CallbackInfo ci) {
        if (RavynClient.getInstance() != null) {
            RavynClient.getInstance().getEventManager().post(new UpdateEvent.Pre());
        }
    }
    
    @Inject(method = "tick", at = @At("TAIL"))
    private void onTickPost(CallbackInfo ci) {
        if (RavynClient.getInstance() != null) {
            RavynClient.getInstance().getEventManager().post(new UpdateEvent.Post());
        }
    }
    
    @Inject(method = "sendMovementPackets", at = @At("HEAD"))
    private void onSendMovementPackets(CallbackInfo ci) {
        // NoFall implementation
        if (RavynClient.getInstance() != null) {
            NoFall noFall = RavynClient.getInstance().getModuleManager().getModule(NoFall.class);
            if (noFall != null && noFall.isEnabled()) {
                ClientPlayerEntity player = (ClientPlayerEntity) (Object) this;
                if (player.fallDistance > 3.0f) {
                    player.networkHandler.sendPacket(new PlayerMoveC2SPacket.OnGroundOnly(true));
                }
            }
        }
    }
    
    @ModifyVariable(method = "pushAwayFrom", at = @At("HEAD"), argsOnly = true)
    private double modifyVelocityX(double x) {
        if (RavynClient.getInstance() != null) {
            Velocity velocity = RavynClient.getInstance().getModuleManager().getModule(Velocity.class);
            if (velocity != null && velocity.isEnabled()) {
                return x * (velocity.getSettings().get(0).getValue() / 100.0);
            }
        }
        return x;
    }
    
    @ModifyVariable(method = "pushAwayFrom", at = @At("HEAD"), argsOnly = true)
    private double modifyVelocityZ(double z) {
        if (RavynClient.getInstance() != null) {
            Velocity velocity = RavynClient.getInstance().getModuleManager().getModule(Velocity.class);
            if (velocity != null && velocity.isEnabled()) {
                return z * (velocity.getSettings().get(0).getValue() / 100.0);
            }
        }
        return z;
    }
}
