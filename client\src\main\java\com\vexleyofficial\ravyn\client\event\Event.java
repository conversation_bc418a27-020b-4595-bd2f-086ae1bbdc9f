package com.vexleyofficial.ravyn.client.event;

/**
 * Base class for all events
 */
public abstract class Event {
    
    private boolean cancelled = false;
    
    /**
     * Check if the event is cancelled
     */
    public boolean isCancelled() {
        return cancelled;
    }
    
    /**
     * Cancel the event
     */
    public void cancel() {
        this.cancelled = true;
    }
    
    /**
     * Set the cancelled state
     */
    public void setCancelled(boolean cancelled) {
        this.cancelled = cancelled;
    }
}
