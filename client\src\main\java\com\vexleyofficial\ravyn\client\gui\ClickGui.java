package com.vexleyofficial.ravyn.client.gui;

import com.vexleyofficial.ravyn.client.RavynClient;
import com.vexleyofficial.ravyn.client.module.Module;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Modern ClickGUI for the client
 */
public class ClickGui extends Screen {
    
    private final List<CategoryPanel> panels = new ArrayList<>();
    private static final int PANEL_WIDTH = 120;
    private static final int PANEL_HEIGHT = 20;
    private static final Color BACKGROUND_COLOR = new Color(20, 20, 30, 200);
    private static final Color PANEL_COLOR = new Color(40, 40, 60, 255);
    private static final Color ENABLED_COLOR = new Color(100, 200, 100, 255);
    private static final Color DISABLED_COLOR = new Color(200, 100, 100, 255);
    
    public ClickGui() {
        super(Text.literal("Ravyn Client"));
        initializePanels();
    }
    
    private void initializePanels() {
        int x = 10;
        int y = 10;
        
        for (Module.Category category : Module.Category.values()) {
            CategoryPanel panel = new CategoryPanel(category, x, y, PANEL_WIDTH);
            panels.add(panel);
            x += PANEL_WIDTH + 10;
        }
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw background
        context.fill(0, 0, width, height, BACKGROUND_COLOR.getRGB());
        
        // Draw title
        context.drawCenteredTextWithShadow(textRenderer, "Ravyn Client v" + RavynClient.VERSION, 
            width / 2, 10, Color.WHITE.getRGB());
        
        // Draw panels
        for (CategoryPanel panel : panels) {
            panel.render(context, mouseX, mouseY, textRenderer);
        }
        
        super.render(context, mouseX, mouseY, delta);
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        for (CategoryPanel panel : panels) {
            if (panel.mouseClicked(mouseX, mouseY, button)) {
                return true;
            }
        }
        return super.mouseClicked(mouseX, mouseY, button);
    }
    
    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        for (CategoryPanel panel : panels) {
            if (panel.mouseDragged(mouseX, mouseY, button, deltaX, deltaY)) {
                return true;
            }
        }
        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }
    
    @Override
    public boolean shouldPause() {
        return false;
    }
    
    /**
     * Category panel for organizing modules
     */
    private static class CategoryPanel {
        private final Module.Category category;
        private final List<Module> modules;
        private int x, y;
        private final int width;
        private boolean expanded = true;
        private boolean dragging = false;
        private int dragOffsetX, dragOffsetY;
        
        public CategoryPanel(Module.Category category, int x, int y, int width) {
            this.category = category;
            this.x = x;
            this.y = y;
            this.width = width;
            this.modules = RavynClient.getInstance().getModuleManager().getModulesByCategory(category);
        }
        
        public void render(DrawContext context, int mouseX, int mouseY, net.minecraft.client.font.TextRenderer textRenderer) {
            int currentY = y;
            
            // Draw category header
            context.fill(x, currentY, x + width, currentY + PANEL_HEIGHT, PANEL_COLOR.getRGB());
            context.drawTextWithShadow(textRenderer, category.getName(), x + 5, currentY + 6, Color.WHITE.getRGB());
            
            // Draw expand/collapse indicator
            String indicator = expanded ? "-" : "+";
            context.drawTextWithShadow(textRenderer, indicator, x + width - 15, currentY + 6, Color.WHITE.getRGB());
            
            currentY += PANEL_HEIGHT;
            
            // Draw modules if expanded
            if (expanded) {
                for (Module module : modules) {
                    Color moduleColor = module.isEnabled() ? ENABLED_COLOR : DISABLED_COLOR;
                    context.fill(x, currentY, x + width, currentY + PANEL_HEIGHT, moduleColor.getRGB());
                    
                    // Draw module name
                    context.drawTextWithShadow(textRenderer, module.getName(), x + 5, currentY + 6, Color.WHITE.getRGB());
                    
                    // Draw enabled indicator
                    if (module.isEnabled()) {
                        context.drawTextWithShadow(textRenderer, "ON", x + width - 25, currentY + 6, Color.WHITE.getRGB());
                    }
                    
                    currentY += PANEL_HEIGHT;
                }
            }
        }
        
        public boolean mouseClicked(double mouseX, double mouseY, int button) {
            int currentY = y;
            
            // Check category header click
            if (mouseX >= x && mouseX <= x + width && mouseY >= currentY && mouseY <= currentY + PANEL_HEIGHT) {
                if (button == 0) { // Left click
                    expanded = !expanded;
                    return true;
                } else if (button == 1) { // Right click - start dragging
                    dragging = true;
                    dragOffsetX = (int) (mouseX - x);
                    dragOffsetY = (int) (mouseY - y);
                    return true;
                }
            }
            
            currentY += PANEL_HEIGHT;
            
            // Check module clicks if expanded
            if (expanded) {
                for (Module module : modules) {
                    if (mouseX >= x && mouseX <= x + width && mouseY >= currentY && mouseY <= currentY + PANEL_HEIGHT) {
                        if (button == 0) { // Left click - toggle module
                            module.toggle();
                            return true;
                        }
                    }
                    currentY += PANEL_HEIGHT;
                }
            }
            
            return false;
        }
        
        public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
            if (dragging && button == 1) {
                x = (int) (mouseX - dragOffsetX);
                y = (int) (mouseY - dragOffsetY);
                return true;
            }
            return false;
        }
    }
}
