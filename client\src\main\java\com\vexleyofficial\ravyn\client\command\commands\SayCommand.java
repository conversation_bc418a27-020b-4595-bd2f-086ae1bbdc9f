package com.vexleyofficial.ravyn.client.command.commands;

import com.vexleyofficial.ravyn.client.command.Command;

/**
 * Say command - Sends a message in chat
 */
public class SayCommand extends Command {
    
    public SayCommand() {
        super("say", "Sends a message in chat", ".say <message>");
    }
    
    @Override
    public void execute(String[] args) throws Exception {
        if (args.length == 0) {
            sendUsage();
            return;
        }
        
        String message = String.join(" ", args);
        
        if (mc.player != null && mc.getNetworkHandler() != null) {
            mc.player.networkHandler.sendChatMessage(message);
            sendSuccess("Message sent: §f" + message);
        } else {
            sendError("Cannot send message - not connected to server.");
        }
    }
}
