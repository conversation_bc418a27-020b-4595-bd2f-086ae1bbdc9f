package com.vexleyofficial.ravyn.launcher.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * Launcher configuration management
 */
public class LauncherConfig {
    
    private static final Logger logger = Logger.getLogger(LauncherConfig.class);
    private static final ObjectMapper mapper = new ObjectMapper();
    private static LauncherConfig instance;
    
    // Configuration paths
    private static final String CONFIG_DIR = System.getProperty("user.home") + "/.ravyn";
    private static final String CONFIG_FILE = CONFIG_DIR + "/launcher.json";
    private static final String MINECRAFT_DIR = CONFIG_DIR + "/minecraft";
    private static final String VERSIONS_DIR = MINECRAFT_DIR + "/versions";
    private static final String LIBRARIES_DIR = MINECRAFT_DIR + "/libraries";
    private static final String ASSETS_DIR = MINECRAFT_DIR + "/assets";
    
    // Configuration properties
    private String selectedVersion = "1.8.9";
    private String selectedProfile = "ravyn";
    private boolean useRavynClient = true;
    private String javaPath = "";
    private String jvmArgs = "-Xmx2G -XX:+UnlockExperimentalVMOptions -XX:+UseG1GC";
    private int windowWidth = 854;
    private int windowHeight = 480;
    private boolean fullscreen = false;
    private String theme = "dark";
    private List<Account> accounts = new ArrayList<>();
    private String selectedAccount = "";
    
    static {
        mapper.enable(SerializationFeature.INDENT_OUTPUT);
    }
    
    public static void initialize() {
        try {
            createDirectories();
            loadConfig();
            logger.info("Launcher configuration initialized");
        } catch (Exception e) {
            logger.error("Failed to initialize launcher configuration", e);
            instance = new LauncherConfig(); // Use defaults
        }
    }
    
    private static void createDirectories() throws IOException {
        Files.createDirectories(Paths.get(CONFIG_DIR));
        Files.createDirectories(Paths.get(MINECRAFT_DIR));
        Files.createDirectories(Paths.get(VERSIONS_DIR));
        Files.createDirectories(Paths.get(LIBRARIES_DIR));
        Files.createDirectories(Paths.get(ASSETS_DIR));
        logger.debug("Created launcher directories");
    }
    
    private static void loadConfig() throws IOException {
        File configFile = new File(CONFIG_FILE);
        if (configFile.exists()) {
            instance = mapper.readValue(configFile, LauncherConfig.class);
            logger.debug("Loaded configuration from {}", CONFIG_FILE);
        } else {
            instance = new LauncherConfig();
            save(); // Create default config file
            logger.debug("Created default configuration");
        }
    }
    
    public static void save() {
        try {
            mapper.writeValue(new File(CONFIG_FILE), instance);
            logger.debug("Saved configuration to {}", CONFIG_FILE);
        } catch (IOException e) {
            logger.error("Failed to save configuration", e);
        }
    }
    
    public static LauncherConfig getInstance() {
        if (instance == null) {
            initialize();
        }
        return instance;
    }
    
    // Getters and setters
    public String getSelectedVersion() { return selectedVersion; }
    public void setSelectedVersion(String selectedVersion) { this.selectedVersion = selectedVersion; }
    
    public String getSelectedProfile() { return selectedProfile; }
    public void setSelectedProfile(String selectedProfile) { this.selectedProfile = selectedProfile; }
    
    public boolean isUseRavynClient() { return useRavynClient; }
    public void setUseRavynClient(boolean useRavynClient) { this.useRavynClient = useRavynClient; }
    
    public String getJavaPath() { return javaPath; }
    public void setJavaPath(String javaPath) { this.javaPath = javaPath; }
    
    public String getJvmArgs() { return jvmArgs; }
    public void setJvmArgs(String jvmArgs) { this.jvmArgs = jvmArgs; }
    
    public int getWindowWidth() { return windowWidth; }
    public void setWindowWidth(int windowWidth) { this.windowWidth = windowWidth; }
    
    public int getWindowHeight() { return windowHeight; }
    public void setWindowHeight(int windowHeight) { this.windowHeight = windowHeight; }
    
    public boolean isFullscreen() { return fullscreen; }
    public void setFullscreen(boolean fullscreen) { this.fullscreen = fullscreen; }
    
    public String getTheme() { return theme; }
    public void setTheme(String theme) { this.theme = theme; }
    
    public List<Account> getAccounts() { return accounts; }
    public void setAccounts(List<Account> accounts) { this.accounts = accounts; }
    
    public String getSelectedAccount() { return selectedAccount; }
    public void setSelectedAccount(String selectedAccount) { this.selectedAccount = selectedAccount; }
    
    // Static path getters
    public static String getConfigDir() { return CONFIG_DIR; }
    public static String getMinecraftDir() { return MINECRAFT_DIR; }
    public static String getVersionsDir() { return VERSIONS_DIR; }
    public static String getLibrariesDir() { return LIBRARIES_DIR; }
    public static String getAssetsDir() { return ASSETS_DIR; }
    
    /**
     * Account data class
     */
    public static class Account {
        private String username;
        private String uuid;
        private String accessToken;
        private String refreshToken;
        private long expiresAt;
        private boolean isOffline;
        
        public Account() {}
        
        public Account(String username, String uuid, boolean isOffline) {
            this.username = username;
            this.uuid = uuid;
            this.isOffline = isOffline;
        }
        
        // Getters and setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public String getUuid() { return uuid; }
        public void setUuid(String uuid) { this.uuid = uuid; }
        
        public String getAccessToken() { return accessToken; }
        public void setAccessToken(String accessToken) { this.accessToken = accessToken; }
        
        public String getRefreshToken() { return refreshToken; }
        public void setRefreshToken(String refreshToken) { this.refreshToken = refreshToken; }
        
        public long getExpiresAt() { return expiresAt; }
        public void setExpiresAt(long expiresAt) { this.expiresAt = expiresAt; }
        
        public boolean isOffline() { return isOffline; }
        public void setOffline(boolean offline) { isOffline = offline; }
        
        public boolean isExpired() {
            return !isOffline && System.currentTimeMillis() > expiresAt;
        }
    }
}
