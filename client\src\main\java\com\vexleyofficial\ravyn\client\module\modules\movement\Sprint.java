package com.vexleyofficial.ravyn.client.module.modules.movement;

import com.vexleyofficial.ravyn.client.module.Module;

/**
 * Sprint module - Automatically sprints
 */
public class Sprint extends Module {
    
    public Sprint() {
        super("Sprint", "Automatically sprints", Category.MOVEMENT);
    }
    
    @Override
    public void onTick() {
        if (mc.player == null) return;
        
        if (mc.player.forwardSpeed > 0 && !mc.player.isSprinting()) {
            mc.player.setSprinting(true);
        }
    }
}
