# Ravyn - Custom Minecraft Launcher & Hacked Client

**Developer:** vexleyofficial  
**Supported Versions:** 1.8.9 - 1.21.5

## Overview

Ravyn is a custom Minecraft launcher with integrated hacked client capabilities, featuring:

- **Custom Launcher**: Modern, fast launcher with account management
- **Hacked Client**: Mixin-based client modifications for enhanced gameplay
- **Multi-Version Support**: Compatible with Minecraft versions 1.8.9 through 1.21.5
- **Easy Installation**: Standalone installer for quick setup

## Features

### Launcher Features
- Microsoft/Mojang account authentication
- Multiple account management
- Version management and auto-updates
- Custom profile configurations
- Mod/client integration
- Modern UI with dark/light themes

### Client Features
- **Combat Enhancements**: KillAura, Reach, AutoClicker, Criticals
- **Movement**: <PERSON>, Speed, NoF<PERSON>, Jesus (Water Walk)
- **Visual**: ESP, Tracers, Fullbright, X-Ray, ChestESP
- **Utility**: AutoMine, Scaffold, Timer, FastPlace
- **Bypass**: Anti-cheat bypass modules
- **GUI**: Modern ClickGUI with customizable themes

## Project Structure

```
Ravyn/
├── launcher/           # Main launcher application
├── client/            # Hacked client implementation
├── installer/         # Standalone installer
├── common/           # Shared utilities and libraries
└── docs/             # Documentation
```

## Development Setup

### Prerequisites
- Java 17+ (for launcher)
- Java 8+ (for client compatibility)
- Maven or Gradle
- IntelliJ IDEA (recommended)

### Building
```bash
# Clone the repository
git clone https://github.com/vexleyofficial/Ravyn.git
cd Ravyn

# Build launcher
cd launcher
mvn clean package

# Build client
cd ../client
./gradlew build

# Build installer
cd ../installer
mvn clean package
```

## Installation

1. Download the latest installer from [Releases](https://github.com/vexleyofficial/Ravyn/releases)
2. Run `RavynInstaller.exe` (Windows) or `RavynInstaller.jar` (Cross-platform)
3. Follow the installation wizard
4. Launch Ravyn from desktop shortcut or start menu

## Usage

### First Launch
1. Open Ravyn Launcher
2. Add your Microsoft/Mojang account
3. Select Minecraft version (1.8.9 - 1.21.5)
4. Choose client profile (Vanilla or Ravyn Client)
5. Click "Launch Game"

### Client Usage
- Press `Right Shift` to open ClickGUI
- Use arrow keys to navigate modules
- Click to toggle modules on/off
- Access settings via gear icon

## Legal Notice

This software is for educational purposes only. Users are responsible for complying with Minecraft's Terms of Service and applicable laws. The developers are not responsible for any consequences of using this software.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

- **Discord**: [Join our server](https://discord.gg/ravyn)
- **Issues**: [GitHub Issues](https://github.com/vexleyofficial/Ravyn/issues)
- **Wiki**: [Documentation](https://github.com/vexleyofficial/Ravyn/wiki)

---

**⚠️ Disclaimer**: This is a hacked client for Minecraft. Use at your own risk and only on servers that allow modifications.
