package com.vexleyofficial.ravyn.client.module.modules.combat;

import com.vexleyofficial.ravyn.client.module.Module;
import com.vexleyofficial.ravyn.client.setting.NumberSetting;

/**
 * Reach module - Extends attack reach
 */
public class Reach extends Module {
    
    private final NumberSetting reach = new NumberSetting("Reach", 3.5, 3.0, 6.0, 0.1);
    
    public Reach() {
        super("Reach", "Extends your attack reach", Category.COMBAT);
        addSetting(reach);
    }
    
    public double getReach() {
        return isEnabled() ? reach.getValue() : 3.0;
    }
}
