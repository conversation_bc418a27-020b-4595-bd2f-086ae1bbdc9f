package com.vexleyofficial.ravyn.client.module.modules.visual;

import com.vexleyofficial.ravyn.client.module.Module;
import com.vexleyofficial.ravyn.client.setting.BooleanSetting;

/**
 * ESP module - Shows entity outlines
 */
public class ESP extends Module {
    
    private final BooleanSetting players = new BooleanSetting("Players", true);
    private final BooleanSetting mobs = new BooleanSetting("Mobs", true);
    private final BooleanSetting items = new BooleanSetting("Items", false);
    
    public ESP() {
        super("ESP", "Shows entity outlines through walls", Category.VISUAL);
        addSetting(players);
        addSetting(mobs);
        addSetting(items);
    }
}
