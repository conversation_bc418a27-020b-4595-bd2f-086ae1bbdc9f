package com.vexleyofficial.ravyn.client.module.modules.visual;

import com.vexleyofficial.ravyn.client.RavynClient;
import com.vexleyofficial.ravyn.client.event.EventHandler;
import com.vexleyofficial.ravyn.client.event.events.RenderEvent;
import com.vexleyofficial.ravyn.client.module.Module;
import com.vexleyofficial.ravyn.client.setting.BooleanSetting;
import net.minecraft.client.font.TextRenderer;
import net.minecraft.client.gui.DrawContext;

import java.awt.*;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * HUD module - Displays client information on screen
 */
public class HUD extends Module {
    
    private final BooleanSetting watermark = new BooleanSetting("Watermark", true);
    private final BooleanSetting moduleList = new BooleanSetting("Module List", true);
    private final BooleanSetting coordinates = new BooleanSetting("Coordinates", true);
    private final BooleanSetting fps = new BooleanSetting("FPS", true);
    private final BooleanSetting ping = new BooleanSetting("Ping", false);
    private final BooleanSetting serverInfo = new BooleanSetting("Server Info", false);
    
    public HUD() {
        super("HUD", "Displays client information", Category.VISUAL);
        
        addSetting(watermark);
        addSetting(moduleList);
        addSetting(coordinates);
        addSetting(fps);
        addSetting(ping);
        addSetting(serverInfo);
        
        // HUD should be enabled by default
        setEnabled(true);
    }
    
    @EventHandler
    public void onRenderHud(RenderEvent.Hud event) {
        if (mc.options.debugEnabled) return; // Don't render over debug screen
        
        DrawContext context = new DrawContext(mc, mc.getBufferBuilders().getEntityVertexConsumers());
        TextRenderer textRenderer = mc.textRenderer;
        
        int screenWidth = mc.getWindow().getScaledWidth();
        int screenHeight = mc.getWindow().getScaledHeight();
        
        // Watermark
        if (watermark.getValue()) {
            String watermarkText = RavynClient.NAME + " v" + RavynClient.VERSION;
            context.drawTextWithShadow(textRenderer, watermarkText, 4, 4, Color.RED.getRGB());
        }
        
        // Module List
        if (moduleList.getValue()) {
            renderModuleList(context, textRenderer, screenWidth);
        }
        
        // Coordinates
        if (coordinates.getValue() && mc.player != null) {
            renderCoordinates(context, textRenderer, screenHeight);
        }
        
        // FPS
        if (fps.getValue()) {
            renderFPS(context, textRenderer, screenWidth, screenHeight);
        }
        
        // Ping
        if (ping.getValue()) {
            renderPing(context, textRenderer, screenWidth, screenHeight);
        }
        
        // Server Info
        if (serverInfo.getValue()) {
            renderServerInfo(context, textRenderer, screenWidth, screenHeight);
        }
    }
    
    private void renderModuleList(DrawContext context, TextRenderer textRenderer, int screenWidth) {
        List<Module> enabledModules = RavynClient.getInstance().getModuleManager().getEnabledModules();
        
        // Filter out HUD module itself
        enabledModules = enabledModules.stream()
            .filter(module -> !module.getName().equals("HUD"))
            .sorted(Comparator.comparing(module -> -textRenderer.getWidth(module.getName())))
            .toList();
        
        int y = 20;
        for (Module module : enabledModules) {
            String moduleName = module.getName();
            int moduleWidth = textRenderer.getWidth(moduleName);
            int x = screenWidth - moduleWidth - 4;
            
            // Background
            context.fill(x - 2, y - 1, screenWidth, y + textRenderer.fontHeight + 1, 
                        new Color(0, 0, 0, 100).getRGB());
            
            // Module name with color based on category
            int color = getCategoryColor(module.getCategory());
            context.drawTextWithShadow(textRenderer, moduleName, x, y, color);
            
            y += textRenderer.fontHeight + 2;
        }
    }
    
    private void renderCoordinates(DrawContext context, TextRenderer textRenderer, int screenHeight) {
        if (mc.player == null) return;
        
        int x = (int) mc.player.getX();
        int y = (int) mc.player.getY();
        int z = (int) mc.player.getZ();
        
        String coordText = String.format("XYZ: %d, %d, %d", x, y, z);
        
        int textY = screenHeight - textRenderer.fontHeight - 4;
        context.fill(2, textY - 2, textRenderer.getWidth(coordText) + 6, 
                    screenHeight - 2, new Color(0, 0, 0, 100).getRGB());
        context.drawTextWithShadow(textRenderer, coordText, 4, textY, Color.WHITE.getRGB());
    }
    
    private void renderFPS(DrawContext context, TextRenderer textRenderer, int screenWidth, int screenHeight) {
        String fpsText = "FPS: " + mc.getCurrentFps();
        int textWidth = textRenderer.getWidth(fpsText);
        int x = screenWidth - textWidth - 4;
        int y = screenHeight - textRenderer.fontHeight - 4;
        
        context.fill(x - 2, y - 2, screenWidth - 2, screenHeight - 2, 
                    new Color(0, 0, 0, 100).getRGB());
        context.drawTextWithShadow(textRenderer, fpsText, x, y, Color.GREEN.getRGB());
    }
    
    private void renderPing(DrawContext context, TextRenderer textRenderer, int screenWidth, int screenHeight) {
        if (mc.getNetworkHandler() == null) return;
        
        int ping = 0;
        if (mc.getNetworkHandler().getPlayerListEntry(mc.player.getUuid()) != null) {
            ping = mc.getNetworkHandler().getPlayerListEntry(mc.player.getUuid()).getLatency();
        }
        
        String pingText = "Ping: " + ping + "ms";
        int textWidth = textRenderer.getWidth(pingText);
        int x = screenWidth - textWidth - 4;
        int y = screenHeight - (textRenderer.fontHeight * 2) - 8;
        
        context.fill(x - 2, y - 2, screenWidth - 2, y + textRenderer.fontHeight + 2, 
                    new Color(0, 0, 0, 100).getRGB());
        
        Color pingColor = ping < 50 ? Color.GREEN : ping < 100 ? Color.YELLOW : Color.RED;
        context.drawTextWithShadow(textRenderer, pingText, x, y, pingColor.getRGB());
    }
    
    private void renderServerInfo(DrawContext context, TextRenderer textRenderer, int screenWidth, int screenHeight) {
        if (mc.getCurrentServerEntry() == null) return;
        
        String serverName = mc.getCurrentServerEntry().name;
        String serverAddress = mc.getCurrentServerEntry().address;
        
        List<String> serverInfo = new ArrayList<>();
        serverInfo.add("Server: " + serverName);
        serverInfo.add("IP: " + serverAddress);
        
        int maxWidth = serverInfo.stream()
            .mapToInt(textRenderer::getWidth)
            .max()
            .orElse(0);
        
        int x = screenWidth - maxWidth - 8;
        int y = 30;
        
        // Background
        context.fill(x - 2, y - 2, screenWidth - 2, 
                    y + (serverInfo.size() * (textRenderer.fontHeight + 2)), 
                    new Color(0, 0, 0, 100).getRGB());
        
        // Text
        for (String info : serverInfo) {
            context.drawTextWithShadow(textRenderer, info, x, y, Color.CYAN.getRGB());
            y += textRenderer.fontHeight + 2;
        }
    }
    
    private int getCategoryColor(Category category) {
        return switch (category) {
            case COMBAT -> Color.RED.getRGB();
            case MOVEMENT -> Color.BLUE.getRGB();
            case VISUAL -> Color.MAGENTA.getRGB();
            case UTILITY -> Color.GREEN.getRGB();
            case MISC -> Color.ORANGE.getRGB();
        };
    }
}
