COMMON DEVELOPMENT AND DISTRIBUTION LICENSE (CDDL) Version 1.1

1. Definitions.

    1.1. "Contributor" means each individual or entity that creates or
    contributes to the creation of Modifications.

    1.2. "Contributor Version" means the combination of the Original
    Software, prior Modifications used by a Contributor (if any), and
    the Modifications made by that particular Contributor.

    1.3. "Covered Software" means (a) the Original Software, or (b)
    Modifications, or (c) the combination of files containing Original
    Software with files containing Modifications, in each case including
    portions thereof.

    1.4. "Executable" means the Covered Software in any form other than
    Source Code.

    1.5. "Initial Developer" means the individual or entity that first
    makes Original Software available under this License.

    1.6. "Larger Work" means a work which combines Covered Software or
    portions thereof with code not governed by the terms of this License.

    1.7. "License" means this document.

    1.8. "Licensable" means having the right to grant, to the maximum
    extent possible, whether at the time of the initial grant or
    subsequently acquired, any and all of the rights conveyed herein.

    1.9. "Modifications" means the Source Code and Executable form of
    any of the following:

    A. Any file that results from an addition to, deletion from or
    modification of the contents of a file containing Original Software
    or previous Modifications;

    B. Any new file that contains any part of the Original Software or
    previous Modification; or

    C. Any new file that is contributed or otherwise made available
    under the terms of this License.

    1.10. "Original Software" means the Source Code and Executable form
    of computer software code that is originally released under this
    License.

    1.11. "Patent Claims" means any patent claim(s), now owned or
    hereafter acquired, including without limitation, method, process,
    and apparatus claims, in any patent Licensable by grantor.

    1.12. "Source Code" means (a) the common form of computer software
    code in which modifications are made and (b) associated
    documentation included in or with such code.

    1.13. "You" (or "Your") means an individual or a legal entity
    exercising rights under, and complying with all of the terms of,
    this License. For legal entities, "You" includes any entity which
    controls, is controlled by, or is under common control with You. For
    purposes of this definition, "control" means (a) the power, direct
    or indirect, to cause the direction or management of such entity,
    whether by contract or otherwise, or (b) ownership of more than
    fifty percent (50%) of the outstanding shares or beneficial
    ownership of such entity.

2. License Grants.

    2.1. The Initial Developer Grant.

    Conditioned upon Your compliance with Section 3.1 below and subject
    to third party intellectual property claims, the Initial Developer
    hereby grants You a world-wide, royalty-free, non-exclusive license:

    (a) under intellectual property rights (other than patent or
    trademark) Licensable by Initial Developer, to use, reproduce,
    modify, display, perform, sublicense and distribute the Original
    Software (or portions thereof), with or without Modifications,
    and/or as part of a Larger Work; and

    (b) under Patent Claims infringed by the making, using or selling of
    Original Software, to make, have made, use, practice, sell, and
    offer for sale, and/or otherwise dispose of the Original Software
    (or portions thereof).

    (c) The licenses granted in Sections 2.1(a) and (b) are effective on
    the date Initial Developer first distributes or otherwise makes the
    Original Software available to a third party under the terms of this
    License.

    (d) Notwithstanding Section 2.1(b) above, no patent license is
    granted: (1) for code that You delete from the Original Software, or
    (2) for infringements caused by: (i) the modification of the
    Original Software, or (ii) the combination of the Original Software
    with other software or devices.

    2.2. Contributor Grant.

    Conditioned upon Your compliance with Section 3.1 below and subject
    to third party intellectual property claims, each Contributor hereby
    grants You a world-wide, royalty-free, non-exclusive license:

    (a) under intellectual property rights (other than patent or
    trademark) Licensable by Contributor to use, reproduce, modify,
    display, perform, sublicense and distribute the Modifications
    created by such Contributor (or portions thereof), either on an
    unmodified basis, with other Modifications, as Covered Software
    and/or as part of a Larger Work; and

    (b) under Patent Claims infringed by the making, using, or selling
    of Modifications made by that Contributor either alone and/or in
    combination with its Contributor Version (or portions of such
    combination), to make, use, sell, offer for sale, have made, and/or
    otherwise dispose of: (1) Modifications made by that Contributor (or
    portions thereof); and (2) the combination of Modifications made by
    that Contributor with its Contributor Version (or portions of such
    combination).

    (c) The licenses granted in Sections 2.2(a) and 2.2(b) are effective
    on the date Contributor first distributes or otherwise makes the
    Modifications available to a third party.

    (d) Notwithstanding Section 2.2(b) above, no patent license is
    granted: (1) for any code that Contributor has deleted from the
    Contributor Version; (2) for infringements caused by: (i) third
    party modifications of Contributor Version, or (ii) the combination
    of Modifications made by that Contributor with other software
    (except as part of the Contributor Version) or other devices; or (3)
    under Patent Claims infringed by Covered Software in the absence of
    Modifications made by that Contributor.

3. Distribution Obligations.

    3.1. Availability of Source Code.

    Any Covered Software that You distribute or otherwise make available
    in Executable form must also be made available in Source Code form
    and that Source Code form must be distributed only under the terms
    of this License. You must include a copy of this License with every
    copy of the Source Code form of the Covered Software You distribute
    or otherwise make available. You must inform recipients of any such
    Covered Software in Executable form as to how they can obtain such
    Covered Software in Source Code form in a reasonable manner on or
    through a medium customarily used for software exchange.

    3.2. Modifications.

    The Modifications that You create or to which You contribute are
    governed by the terms of this License. You represent that You
    believe Your Modifications are Your original creation(s) and/or You
    have sufficient rights to grant the rights conveyed by this License.

    3.3. Required Notices.

    You must include a notice in each of Your Modifications that
    identifies You as the Contributor of the Modification. You may not
    remove or alter any copyright, patent or trademark notices contained
    within the Covered Software, or any notices of licensing or any
    descriptive text giving attribution to any Contributor or the
    Initial Developer.

    3.4. Application of Additional Terms.

    You may not offer or impose any terms on any Covered Software in
    Source Code form that alters or restricts the applicable version of
    this License or the recipients' rights hereunder. You may choose to
    offer, and to charge a fee for, warranty, support, indemnity or
    liability obligations to one or more recipients of Covered Software.
    However, you may do so only on Your own behalf, and not on behalf of
    the Initial Developer or any Contributor. You must make it
    absolutely clear that any such warranty, support, indemnity or
    liability obligation is offered by You alone, and You hereby agree
    to indemnify the Initial Developer and every Contributor for any
    liability incurred by the Initial Developer or such Contributor as a
    result of warranty, support, indemnity or liability terms You offer.

    3.5. Distribution of Executable Versions.

    You may distribute the Executable form of the Covered Software under
    the terms of this License or under the terms of a license of Your
    choice, which may contain terms different from this License,
    provided that You are in compliance with the terms of this License
    and that the license for the Executable form does not attempt to
    limit or alter the recipient's rights in the Source Code form from
    the rights set forth in this License. If You distribute the Covered
    Software in Executable form under a different license, You must make
    it absolutely clear that any terms which differ from this License
    are offered by You alone, not by the Initial Developer or
    Contributor. You hereby agree to indemnify the Initial Developer and
    every Contributor for any liability incurred by the Initial
    Developer or such Contributor as a result of any such terms You offer.

    3.6. Larger Works.

    You may create a Larger Work by combining Covered Software with
    other code not governed by the terms of this License and distribute
    the Larger Work as a single product. In such a case, You must make
    sure the requirements of this License are fulfilled for the Covered
    Software.

4. Versions of the License.

    4.1. New Versions.

    Oracle is the initial license steward and may publish revised and/or
    new versions of this License from time to time. Each version will be
    given a distinguishing version number. Except as provided in Section
    4.3, no one other than the license steward has the right to modify
    this License.

    4.2. Effect of New Versions.

    You may always continue to use, distribute or otherwise make the
    Covered Software available under the terms of the version of the
    License under which You originally received the Covered Software. If
    the Initial Developer includes a notice in the Original Software
    prohibiting it from being distributed or otherwise made available
    under any subsequent version of the License, You must distribute and
    make the Covered Software available under the terms of the version
    of the License under which You originally received the Covered
    Software. Otherwise, You may also choose to use, distribute or
    otherwise make the Covered Software available under the terms of any
    subsequent version of the License published by the license steward.

    4.3. Modified Versions.

    When You are an Initial Developer and You want to create a new
    license for Your Original Software, You may create and use a
    modified version of this License if You: (a) rename the license and
    remove any references to the name of the license steward (except to
    note that the license differs from this License); and (b) otherwise
    make it clear that the license contains terms which differ from this
    License.

5. DISCLAIMER OF WARRANTY.

    COVERED SOFTWARE IS PROVIDED UNDER THIS LICENSE ON AN "AS IS" BASIS,
    WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESSED OR IMPLIED,
    INCLUDING, WITHOUT LIMITATION, WARRANTIES THAT THE COVERED SOFTWARE
    IS FREE OF DEFECTS, MERCHANTABLE, FIT FOR A PARTICULAR PURPOSE OR
    NON-INFRINGING. THE ENTIRE RISK AS TO THE QUALITY AND PERFORMANCE OF
    THE COVERED SOFTWARE IS WITH YOU. SHOULD ANY COVERED SOFTWARE PROVE
    DEFECTIVE IN ANY RESPECT, YOU (NOT THE INITIAL DEVELOPER OR ANY
    OTHER CONTRIBUTOR) ASSUME THE COST OF ANY NECESSARY SERVICING,
    REPAIR OR CORRECTION. THIS DISCLAIMER OF WARRANTY CONSTITUTES AN
    ESSENTIAL PART OF THIS LICENSE. NO USE OF ANY COVERED SOFTWARE IS
    AUTHORIZED HEREUNDER EXCEPT UNDER THIS DISCLAIMER.

6. TERMINATION.

    6.1. This License and the rights granted hereunder will terminate
    automatically if You fail to comply with terms herein and fail to
    cure such breach within 30 days of becoming aware of the breach.
    Provisions which, by their nature, must remain in effect beyond the
    termination of this License shall survive.

    6.2. If You assert a patent infringement claim (excluding
    declaratory judgment actions) against Initial Developer or a
    Contributor (the Initial Developer or Contributor against whom You
    assert such claim is referred to as "Participant") alleging that the
    Participant Software (meaning the Contributor Version where the
    Participant is a Contributor or the Original Software where the
    Participant is the Initial Developer) directly or indirectly
    infringes any patent, then any and all rights granted directly or
    indirectly to You by such Participant, the Initial Developer (if the
    Initial Developer is not the Participant) and all Contributors under
    Sections 2.1 and/or 2.2 of this License shall, upon 60 days notice
    from Participant terminate prospectively and automatically at the
    expiration of such 60 day notice period, unless if within such 60
    day period You withdraw Your claim with respect to the Participant
    Software against such Participant either unilaterally or pursuant to
    a written agreement with Participant.

    6.3. If You assert a patent infringement claim against Participant
    alleging that the Participant Software directly or indirectly
    infringes any patent where such claim is resolved (such as by
    license or settlement) prior to the initiation of patent
    infringement litigation, then the reasonable value of the licenses
    granted by such Participant under Sections 2.1 or 2.2 shall be taken
    into account in determining the amount or value of any payment or
    license.

    6.4. In the event of termination under Sections 6.1 or 6.2 above,
    all end user licenses that have been validly granted by You or any
    distributor hereunder prior to termination (excluding licenses
    granted to You by any distributor) shall survive termination.

7. LIMITATION OF LIABILITY.

    UNDER NO CIRCUMSTANCES AND UNDER NO LEGAL THEORY, WHETHER TORT
    (INCLUDING NEGLIGENCE), CONTRACT, OR OTHERWISE, SHALL YOU, THE
    INITIAL DEVELOPER, ANY OTHER CONTRIBUTOR, OR ANY DISTRIBUTOR OF
    COVERED SOFTWARE, OR ANY SUPPLIER OF ANY OF SUCH PARTIES, BE LIABLE
    TO ANY PERSON FOR ANY INDIRECT, SPECIAL, INCIDENTAL, OR
    CONSEQUENTIAL DAMAGES OF ANY CHARACTER INCLUDING, WITHOUT
    LIMITATION, DAMAGES FOR LOSS OF GOODWILL, WORK STOPPAGE, COMPUTER
    FAILURE OR MALFUNCTION, OR ANY AND ALL OTHER COMMERCIAL DAMAGES OR
    LOSSES, EVEN IF SUCH PARTY SHALL HAVE BEEN INFORMED OF THE
    POSSIBILITY OF SUCH DAMAGES. THIS LIMITATION OF LIABILITY SHALL NOT
    APPLY TO LIABILITY FOR DEATH OR PERSONAL INJURY RESULTING FROM SUCH
    PARTY'S NEGLIGENCE TO THE EXTENT APPLICABLE LAW PROHIBITS SUCH
    LIMITATION. SOME JURISDICTIONS DO NOT ALLOW THE EXCLUSION OR
    LIMITATION OF INCIDENTAL OR CONSEQUENTIAL DAMAGES, SO THIS EXCLUSION
    AND LIMITATION MAY NOT APPLY TO YOU.

8. U.S. GOVERNMENT END USERS.

    The Covered Software is a "commercial item," as that term is defined
    in 48 C.F.R. 2.101 (Oct. 1995), consisting of "commercial computer
    software" (as that term is defined at 48 C.F.R. §
    252.227-7014(a)(1)) and "commercial computer software documentation"
    as such terms are used in 48 C.F.R. 12.212 (Sept. 1995). Consistent
    with 48 C.F.R. 12.212 and 48 C.F.R. 227.7202-1 through 227.7202-4
    (June 1995), all U.S. Government End Users acquire Covered Software
    with only those rights set forth herein. This U.S. Government Rights
    clause is in lieu of, and supersedes, any other FAR, DFAR, or other
    clause or provision that addresses Government rights in computer
    software under this License.

9. MISCELLANEOUS.

    This License represents the complete agreement concerning subject
    matter hereof. If any provision of this License is held to be
    unenforceable, such provision shall be reformed only to the extent
    necessary to make it enforceable. This License shall be governed by
    the law of the jurisdiction specified in a notice contained within
    the Original Software (except to the extent applicable law, if any,
    provides otherwise), excluding such jurisdiction's conflict-of-law
    provisions. Any litigation relating to this License shall be subject
    to the jurisdiction of the courts located in the jurisdiction and
    venue specified in a notice contained within the Original Software,
    with the losing party responsible for costs, including, without
    limitation, court costs and reasonable attorneys' fees and expenses.
    The application of the United Nations Convention on Contracts for
    the International Sale of Goods is expressly excluded. Any law or
    regulation which provides that the language of a contract shall be
    construed against the drafter shall not apply to this License. You
    agree that You alone are responsible for compliance with the United
    States export administration regulations (and the export control
    laws and regulation of any other countries) when You use, distribute
    or otherwise make available any Covered Software.

10. RESPONSIBILITY FOR CLAIMS.

    As between Initial Developer and the Contributors, each party is
    responsible for claims and damages arising, directly or indirectly,
    out of its utilization of rights under this License and You agree to
    work with Initial Developer and Contributors to distribute such
    responsibility on an equitable basis. Nothing herein is intended or
    shall be deemed to constitute any admission of liability.

------------------------------------------------------------------------

NOTICE PURSUANT TO SECTION 9 OF THE COMMON DEVELOPMENT AND DISTRIBUTION
LICENSE (CDDL)

The code released under the CDDL shall be governed by the laws of the
State of California (excluding conflict-of-law provisions). Any
litigation relating to this License shall be subject to the jurisdiction
of the Federal Courts of the Northern District of California and the
state courts of the State of California, with venue lying in Santa Clara
County, California.



  The GNU General Public License (GPL) Version 2, June 1991

Copyright (C) 1989, 1991 Free Software Foundation, Inc.
51 Franklin Street, Fifth Floor
Boston, MA 02110-1335
USA

Everyone is permitted to copy and distribute verbatim copies
of this license document, but changing it is not allowed.

Preamble

The licenses for most software are designed to take away your freedom to
share and change it. By contrast, the GNU General Public License is
intended to guarantee your freedom to share and change free software--to
make sure the software is free for all its users. This General Public
License applies to most of the Free Software Foundation's software and
to any other program whose authors commit to using it. (Some other Free
Software Foundation software is covered by the GNU Library General
Public License instead.) You can apply it to your programs, too.

When we speak of free software, we are referring to freedom, not price.
Our General Public Licenses are designed to make sure that you have the
freedom to distribute copies of free software (and charge for this
service if you wish), that you receive source code or can get it if you
want it, that you can change the software or use pieces of it in new
free programs; and that you know you can do these things.

To protect your rights, we need to make restrictions that forbid anyone
to deny you these rights or to ask you to surrender the rights. These
restrictions translate to certain responsibilities for you if you
distribute copies of the software, or if you modify it.

For example, if you distribute copies of such a program, whether gratis
or for a fee, you must give the recipients all the rights that you have.
You must make sure that they, too, receive or can get the source code.
And you must show them these terms so they know their rights.

We protect your rights with two steps: (1) copyright the software, and
(2) offer you this license which gives you legal permission to copy,
distribute and/or modify the software.

Also, for each author's protection and ours, we want to make certain
that everyone understands that there is no warranty for this free
software. If the software is modified by someone else and passed on, we
want its recipients to know that what they have is not the original, so
that any problems introduced by others will not reflect on the original
authors' reputations.

Finally, any free program is threatened constantly by software patents.
We wish to avoid the danger that redistributors of a free program will
individually obtain patent licenses, in effect making the program
proprietary. To prevent this, we have made it clear that any patent must
be licensed for everyone's free use or not licensed at all.

The precise terms and conditions for copying, distribution and
modification follow.

TERMS AND CONDITIONS FOR COPYING, DISTRIBUTION AND MODIFICATION

0. This License applies to any program or other work which contains a
notice placed by the copyright holder saying it may be distributed under
the terms of this General Public License. The "Program", below, refers
to any such program or work, and a "work based on the Program" means
either the Program or any derivative work under copyright law: that is
to say, a work containing the Program or a portion of it, either
verbatim or with modifications and/or translated into another language.
(Hereinafter, translation is included without limitation in the term
"modification".) Each licensee is addressed as "you".

Activities other than copying, distribution and modification are not
covered by this License; they are outside its scope. The act of running
the Program is not restricted, and the output from the Program is
covered only if its contents constitute a work based on the Program
(independent of having been made by running the Program). Whether that
is true depends on what the Program does.

1. You may copy and distribute verbatim copies of the Program's source
code as you receive it, in any medium, provided that you conspicuously
and appropriately publish on each copy an appropriate copyright notice
and disclaimer of warranty; keep intact all the notices that refer to
this License and to the absence of any warranty; and give any other
recipients of the Program a copy of this License along with the Program.

You may charge a fee for the physical act of transferring a copy, and
you may at your option offer warranty protection in exchange for a fee.

2. You may modify your copy or copies of the Program or any portion of
it, thus forming a work based on the Program, and copy and distribute
such modifications or work under the terms of Section 1 above, provided
that you also meet all of these conditions:

    a) You must cause the modified files to carry prominent notices
    stating that you changed the files and the date of any change.

    b) You must cause any work that you distribute or publish, that in
    whole or in part contains or is derived from the Program or any part
    thereof, to be licensed as a whole at no charge to all third parties
    under the terms of this License.

    c) If the modified program normally reads commands interactively
    when run, you must cause it, when started running for such
    interactive use in the most ordinary way, to print or display an
    announcement including an appropriate copyright notice and a notice
    that there is no warranty (or else, saying that you provide a
    warranty) and that users may redistribute the program under these
    conditions, and telling the user how to view a copy of this License.
    (Exception: if the Program itself is interactive but does not
    normally print such an announcement, your work based on the Program
    is not required to print an announcement.)

These requirements apply to the modified work as a whole. If
identifiable sections of that work are not derived from the Program, and
can be reasonably considered independent and separate works in
themselves, then this License, and its terms, do not apply to those
sections when you distribute them as separate works. But when you
distribute the same sections as part of a whole which is a work based on
the Program, the distribution of the whole must be on the terms of this
License, whose permissions for other licensees extend to the entire
whole, and thus to each and every part regardless of who wrote it.

Thus, it is not the intent of this section to claim rights or contest
your rights to work written entirely by you; rather, the intent is to
exercise the right to control the distribution of derivative or
collective works based on the Program.

In addition, mere aggregation of another work not based on the Program
with the Program (or with a work based on the Program) on a volume of a
storage or distribution medium does not bring the other work under the
scope of this License.

3. You may copy and distribute the Program (or a work based on it,
under Section 2) in object code or executable form under the terms of
Sections 1 and 2 above provided that you also do one of the following:

    a) Accompany it with the complete corresponding machine-readable
    source code, which must be distributed under the terms of Sections 1
    and 2 above on a medium customarily used for software interchange; or,

    b) Accompany it with a written offer, valid for at least three
    years, to give any third party, for a charge no more than your cost
    of physically performing source distribution, a complete
    machine-readable copy of the corresponding source code, to be
    distributed under the terms of Sections 1 and 2 above on a medium
    customarily used for software interchange; or,

    c) Accompany it with the information you received as to the offer to
    distribute corresponding source code. (This alternative is allowed
    only for noncommercial distribution and only if you received the
    program in object code or executable form with such an offer, in
    accord with Subsection b above.)

The source code for a work means the preferred form of the work for
making modifications to it. For an executable work, complete source code
means all the source code for all modules it contains, plus any
associated interface definition files, plus the scripts used to control
compilation and installation of the executable. However, as a special
exception, the source code distributed need not include anything that is
normally distributed (in either source or binary form) with the major
components (compiler, kernel, and so on) of the operating system on
which the executable runs, unless that component itself accompanies the
executable.

If distribution of executable or object code is made by offering access
to copy from a designated place, then offering equivalent access to copy
the source code from the same place counts as distribution of the source
code, even though third parties are not compelled to copy the source
along with the object code.

4. You may not copy, modify, sublicense, or distribute the Program
except as expressly provided under this License. Any attempt otherwise
to copy, modify, sublicense or distribute the Program is void, and will
automatically terminate your rights under this License. However, parties
who have received copies, or rights, from you under this License will
not have their licenses terminated so long as such parties remain in
full compliance.

5. You are not required to accept this License, since you have not
signed it. However, nothing else grants you permission to modify or
distribute the Program or its derivative works. These actions are
prohibited by law if you do not accept this License. Therefore, by
modifying or distributing the Program (or any work based on the
Program), you indicate your acceptance of this License to do so, and all
its terms and conditions for copying, distributing or modifying the
Program or works based on it.

6. Each time you redistribute the Program (or any work based on the
Program), the recipient automatically receives a license from the
original licensor to copy, distribute or modify the Program subject to
these terms and conditions. You may not impose any further restrictions
on the recipients' exercise of the rights granted herein. You are not
responsible for enforcing compliance by third parties to this License.

7. If, as a consequence of a court judgment or allegation of patent
infringement or for any other reason (not limited to patent issues),
conditions are imposed on you (whether by court order, agreement or
otherwise) that contradict the conditions of this License, they do not
excuse you from the conditions of this License. If you cannot distribute
so as to satisfy simultaneously your obligations under this License and
any other pertinent obligations, then as a consequence you may not
distribute the Program at all. For example, if a patent license would
not permit royalty-free redistribution of the Program by all those who
receive copies directly or indirectly through you, then the only way you
could satisfy both it and this License would be to refrain entirely from
distribution of the Program.

If any portion of this section is held invalid or unenforceable under
any particular circumstance, the balance of the section is intended to
apply and the section as a whole is intended to apply in other
circumstances.

It is not the purpose of this section to induce you to infringe any
patents or other property right claims or to contest validity of any
such claims; this section has the sole purpose of protecting the
integrity of the free software distribution system, which is implemented
by public license practices. Many people have made generous
contributions to the wide range of software distributed through that
system in reliance on consistent application of that system; it is up to
the author/donor to decide if he or she is willing to distribute
software through any other system and a licensee cannot impose that choice.

This section is intended to make thoroughly clear what is believed to be
a consequence of the rest of this License.

8. If the distribution and/or use of the Program is restricted in
certain countries either by patents or by copyrighted interfaces, the
original copyright holder who places the Program under this License may
add an explicit geographical distribution limitation excluding those
countries, so that distribution is permitted only in or among countries
not thus excluded. In such case, this License incorporates the
limitation as if written in the body of this License.

9. The Free Software Foundation may publish revised and/or new
versions of the General Public License from time to time. Such new
versions will be similar in spirit to the present version, but may
differ in detail to address new problems or concerns.

Each version is given a distinguishing version number. If the Program
specifies a version number of this License which applies to it and "any
later version", you have the option of following the terms and
conditions either of that version or of any later version published by
the Free Software Foundation. If the Program does not specify a version
number of this License, you may choose any version ever published by the
Free Software Foundation.

10. If you wish to incorporate parts of the Program into other free
programs whose distribution conditions are different, write to the
author to ask for permission. For software which is copyrighted by the
Free Software Foundation, write to the Free Software Foundation; we
sometimes make exceptions for this. Our decision will be guided by the
two goals of preserving the free status of all derivatives of our free
software and of promoting the sharing and reuse of software generally.

NO WARRANTY

11. BECAUSE THE PROGRAM IS LICENSED FREE OF CHARGE, THERE IS NO
WARRANTY FOR THE PROGRAM, TO THE EXTENT PERMITTED BY APPLICABLE LAW.
EXCEPT WHEN OTHERWISE STATED IN WRITING THE COPYRIGHT HOLDERS AND/OR
OTHER PARTIES PROVIDE THE PROGRAM "AS IS" WITHOUT WARRANTY OF ANY KIND,
EITHER EXPRESSED OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE. THE
ENTIRE RISK AS TO THE QUALITY AND PERFORMANCE OF THE PROGRAM IS WITH
YOU. SHOULD THE PROGRAM PROVE DEFECTIVE, YOU ASSUME THE COST OF ALL
NECESSARY SERVICING, REPAIR OR CORRECTION.

12. IN NO EVENT UNLESS REQUIRED BY APPLICABLE LAW OR AGREED TO IN
WRITING WILL ANY COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MAY MODIFY
AND/OR REDISTRIBUTE THE PROGRAM AS PERMITTED ABOVE, BE LIABLE TO YOU FOR
DAMAGES, INCLUDING ANY GENERAL, SPECIAL, INCIDENTAL OR CONSEQUENTIAL
DAMAGES ARISING OUT OF THE USE OR INABILITY TO USE THE PROGRAM
(INCLUDING BUT NOT LIMITED TO LOSS OF DATA OR DATA BEING RENDERED
INACCURATE OR LOSSES SUSTAINED BY YOU OR THIRD PARTIES OR A FAILURE OF
THE PROGRAM TO OPERATE WITH ANY OTHER PROGRAMS), EVEN IF SUCH HOLDER OR
OTHER PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.

END OF TERMS AND CONDITIONS

How to Apply These Terms to Your New Programs

If you develop a new program, and you want it to be of the greatest
possible use to the public, the best way to achieve this is to make it
free software which everyone can redistribute and change under these terms.

To do so, attach the following notices to the program. It is safest to
attach them to the start of each source file to most effectively convey
the exclusion of warranty; and each file should have at least the
"copyright" line and a pointer to where the full notice is found.

    One line to give the program's name and a brief idea of what it does.
    Copyright (C) <year> <name of author>

    This program is free software; you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation; either version 2 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful, but
    WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
    General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA 02110-1335 USA

Also add information on how to contact you by electronic and paper mail.

If the program is interactive, make it output a short notice like this
when it starts in an interactive mode:

    Gnomovision version 69, Copyright (C) year name of author
    Gnomovision comes with ABSOLUTELY NO WARRANTY; for details type
    `show w'. This is free software, and you are welcome to redistribute
    it under certain conditions; type `show c' for details.

The hypothetical commands `show w' and `show c' should show the
appropriate parts of the General Public License. Of course, the commands
you use may be called something other than `show w' and `show c'; they
could even be mouse-clicks or menu items--whatever suits your program.

You should also get your employer (if you work as a programmer) or your
school, if any, to sign a "copyright disclaimer" for the program, if
necessary. Here is a sample; alter the names:

    Yoyodyne, Inc., hereby disclaims all copyright interest in the
    program `Gnomovision' (which makes passes at compilers) written by
    James Hacker.

    signature of Ty Coon, 1 April 1989
    Ty Coon, President of Vice

This General Public License does not permit incorporating your program
into proprietary programs. If your program is a subroutine library, you
may consider it more useful to permit linking proprietary applications
with the library. If this is what you want to do, use the GNU Library
General Public License instead of this License.

#

Certain source files distributed by Oracle America, Inc. and/or its
affiliates are subject to the following clarification and special
exception to the GPLv2, based on the GNU Project exception for its
Classpath libraries, known as the GNU Classpath Exception, but only
where Oracle has expressly included in the particular source file's
header the words "Oracle designates this particular file as subject to
the "Classpath" exception as provided by Oracle in the LICENSE file
that accompanied this code."

You should also note that Oracle includes multiple, independent
programs in this software package. Some of those programs are provided
under licenses deemed incompatible with the GPLv2 by the Free Software
Foundation and others.  For example, the package includes programs
licensed under the Apache License, Version 2.0.  Such programs are
licensed to you under their original licenses.

Oracle facilitates your further distribution of this package by adding
the Classpath Exception to the necessary parts of its GPLv2 code, which
permits you to use that code in combination with other independent
modules not licensed under the GPLv2.  However, note that this would
not permit you to commingle code under an incompatible license with
Oracle's GPLv2 licensed code by, for example, cutting and pasting such
code into a file also containing Oracle's GPLv2 licensed code and then
distributing the result.  Additionally, if you were to remove the
Classpath Exception from any of the files to which it applies and
distribute the result, you would likely be required to license some or
all of the other code in that distribution under the GPLv2 as well, and
since the GPLv2 is incompatible with the license terms of some items
included in the distribution by Oracle, removing the Classpath
Exception could therefore effectively compromise your ability to
further distribute the package.

Proceed with caution and we recommend that you obtain the advice of a
lawyer skilled in open source matters before removing the Classpath
Exception or making modifications to this package which may
subsequently be redistributed and/or involve the use of third party
software.

CLASSPATH EXCEPTION
Linking this library statically or dynamically with other modules is
making a combined work based on this library.  Thus, the terms and
conditions of the GNU General Public License version 2 cover the whole
combination.

As a special exception, the copyright holders of this library give you
permission to link this library with independent modules to produce an
executable, regardless of the license terms of these independent
modules, and to copy and distribute the resulting executable under
terms of your choice, provided that you also meet, for each linked
independent module, the terms and conditions of the license of that
module.  An independent module is a module which is not derived from or
based on this library.  If you modify this library, you may extend this
exception to your version of the library, but you are not obligated to
do so.  If you do not wish to do so, delete this exception statement
from your version.
