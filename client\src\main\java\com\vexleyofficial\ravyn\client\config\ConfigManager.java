package com.vexleyofficial.ravyn.client.config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.vexleyofficial.ravyn.client.RavynClient;
import com.vexleyofficial.ravyn.client.module.Module;
import com.vexleyofficial.ravyn.client.setting.BooleanSetting;
import com.vexleyofficial.ravyn.client.setting.NumberSetting;
import com.vexleyofficial.ravyn.client.setting.Setting;
import com.vexleyofficial.ravyn.client.util.Logger;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * Manages client configuration saving and loading
 */
public class ConfigManager {
    
    private static final Logger logger = Logger.getLogger(ConfigManager.class);
    private static final Gson gson = new GsonBuilder().setPrettyPrinting().create();
    
    private static final String CONFIG_DIR = System.getProperty("user.home") + "/.ravyn/configs";
    private static final String DEFAULT_CONFIG = "default.json";
    
    public ConfigManager() {
        try {
            Files.createDirectories(Paths.get(CONFIG_DIR));
            logger.info("Config manager initialized");
        } catch (IOException e) {
            logger.error("Failed to create config directory", e);
        }
    }
    
    /**
     * Save current configuration
     */
    public boolean saveConfig(String name) {
        try {
            JsonObject config = new JsonObject();
            
            // Save modules
            JsonObject modules = new JsonObject();
            for (Module module : RavynClient.getInstance().getModuleManager().getModules()) {
                JsonObject moduleData = new JsonObject();
                moduleData.addProperty("enabled", module.isEnabled());
                moduleData.addProperty("key", module.getKey());
                
                // Save settings
                JsonObject settings = new JsonObject();
                for (Setting<?> setting : module.getSettings()) {
                    if (setting instanceof BooleanSetting boolSetting) {
                        settings.addProperty(setting.getName(), boolSetting.getValue());
                    } else if (setting instanceof NumberSetting numSetting) {
                        settings.addProperty(setting.getName(), numSetting.getValue());
                    }
                }
                moduleData.add("settings", settings);
                
                modules.add(module.getName(), moduleData);
            }
            config.add("modules", modules);
            
            // Save friends
            JsonObject friends = new JsonObject();
            friends.add("friends", gson.toJsonTree(RavynClient.getInstance().getFriendManager().getFriends()));
            friends.add("enemies", gson.toJsonTree(RavynClient.getInstance().getFriendManager().getEnemies()));
            config.add("friends", friends);
            
            // Save to file
            File configFile = new File(CONFIG_DIR, name + ".json");
            try (FileWriter writer = new FileWriter(configFile)) {
                gson.toJson(config, writer);
            }
            
            logger.info("Saved configuration: {}", name);
            return true;
            
        } catch (Exception e) {
            logger.error("Failed to save configuration: " + name, e);
            return false;
        }
    }
    
    /**
     * Load configuration
     */
    public boolean loadConfig(String name) {
        try {
            File configFile = new File(CONFIG_DIR, name + ".json");
            if (!configFile.exists()) {
                logger.warn("Configuration file not found: {}", name);
                return false;
            }
            
            JsonObject config;
            try (FileReader reader = new FileReader(configFile)) {
                config = JsonParser.parseReader(reader).getAsJsonObject();
            }
            
            // Load modules
            if (config.has("modules")) {
                JsonObject modules = config.getAsJsonObject("modules");
                
                for (Module module : RavynClient.getInstance().getModuleManager().getModules()) {
                    if (modules.has(module.getName())) {
                        JsonObject moduleData = modules.getAsJsonObject(module.getName());
                        
                        // Load enabled state
                        if (moduleData.has("enabled")) {
                            module.setEnabled(moduleData.get("enabled").getAsBoolean());
                        }
                        
                        // Load keybind
                        if (moduleData.has("key")) {
                            module.setKey(moduleData.get("key").getAsInt());
                        }
                        
                        // Load settings
                        if (moduleData.has("settings")) {
                            JsonObject settings = moduleData.getAsJsonObject("settings");
                            
                            for (Setting<?> setting : module.getSettings()) {
                                if (settings.has(setting.getName())) {
                                    if (setting instanceof BooleanSetting boolSetting) {
                                        boolSetting.setValue(settings.get(setting.getName()).getAsBoolean());
                                    } else if (setting instanceof NumberSetting numSetting) {
                                        numSetting.setValue(settings.get(setting.getName()).getAsDouble());
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            // Load friends
            if (config.has("friends")) {
                JsonObject friends = config.getAsJsonObject("friends");
                
                if (friends.has("friends")) {
                    RavynClient.getInstance().getFriendManager().clearFriends();
                    for (var friend : friends.getAsJsonArray("friends")) {
                        RavynClient.getInstance().getFriendManager().addFriend(friend.getAsString());
                    }
                }
                
                if (friends.has("enemies")) {
                    RavynClient.getInstance().getFriendManager().clearEnemies();
                    for (var enemy : friends.getAsJsonArray("enemies")) {
                        RavynClient.getInstance().getFriendManager().addEnemy(enemy.getAsString());
                    }
                }
            }
            
            logger.info("Loaded configuration: {}", name);
            return true;
            
        } catch (Exception e) {
            logger.error("Failed to load configuration: " + name, e);
            return false;
        }
    }
    
    /**
     * Get list of available configurations
     */
    public List<String> getConfigs() {
        List<String> configs = new ArrayList<>();
        
        File configDir = new File(CONFIG_DIR);
        if (configDir.exists() && configDir.isDirectory()) {
            File[] files = configDir.listFiles((dir, name) -> name.endsWith(".json"));
            if (files != null) {
                for (File file : files) {
                    String name = file.getName();
                    configs.add(name.substring(0, name.lastIndexOf('.')));
                }
            }
        }
        
        return configs;
    }
    
    /**
     * Delete a configuration
     */
    public boolean deleteConfig(String name) {
        try {
            File configFile = new File(CONFIG_DIR, name + ".json");
            if (configFile.exists()) {
                boolean deleted = configFile.delete();
                if (deleted) {
                    logger.info("Deleted configuration: {}", name);
                }
                return deleted;
            }
            return false;
        } catch (Exception e) {
            logger.error("Failed to delete configuration: " + name, e);
            return false;
        }
    }
    
    /**
     * Save default configuration on shutdown
     */
    public void saveDefaultConfig() {
        saveConfig("default");
    }
    
    /**
     * Load default configuration on startup
     */
    public void loadDefaultConfig() {
        if (new File(CONFIG_DIR, DEFAULT_CONFIG).exists()) {
            loadConfig("default");
        }
    }
}
