package com.vexleyofficial.ravyn.client;

import com.vexleyofficial.ravyn.client.command.CommandManager;
import com.vexleyofficial.ravyn.client.config.ConfigManager;
import com.vexleyofficial.ravyn.client.event.EventManager;
import com.vexleyofficial.ravyn.client.friend.FriendManager;
import com.vexleyofficial.ravyn.client.gui.ClickGui;
import com.vexleyofficial.ravyn.client.module.ModuleManager;
import com.vexleyofficial.ravyn.client.setting.SettingManager;
import com.vexleyofficial.ravyn.client.util.Logger;
import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import org.lwjgl.glfw.GLFW;

/**
 * Ravyn Client - Advanced Minecraft Hacked Client
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class RavynClient implements ClientModInitializer {
    
    public static final String NAME = "Ravyn Client";
    public static final String VERSION = "1.0.0";
    public static final String AUTHOR = "vexleyofficial";
    
    private static RavynClient instance;
    private static final Logger logger = Logger.getLogger(RavynClient.class);
    
    // Core managers
    private EventManager eventManager;
    private ModuleManager moduleManager;
    private SettingManager settingManager;
    private CommandManager commandManager;
    private FriendManager friendManager;
    private ConfigManager configManager;
    private ClickGui clickGui;
    
    // Key bindings
    private KeyBinding clickGuiKey;
    
    @Override
    public void onInitializeClient() {
        instance = this;
        
        logger.info("Initializing {} v{} by {}", NAME, VERSION, AUTHOR);
        
        try {
            // Initialize core systems
            initializeManagers();
            
            // Register key bindings
            registerKeyBindings();
            
            // Register event listeners
            registerEventListeners();
            
            logger.info("{} initialized successfully!", NAME);
            
        } catch (Exception e) {
            logger.error("Failed to initialize Ravyn Client", e);
        }
    }
    
    private void initializeManagers() {
        logger.info("Initializing core managers...");
        
        // Initialize in correct order
        eventManager = new EventManager();
        settingManager = new SettingManager();
        moduleManager = new ModuleManager();
        commandManager = new CommandManager();
        friendManager = new FriendManager();
        configManager = new ConfigManager();
        clickGui = new ClickGui();

        // Load default configuration
        configManager.loadDefaultConfig();
        
        logger.info("Core managers initialized");
    }
    
    private void registerKeyBindings() {
        logger.info("Registering key bindings...");
        
        clickGuiKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
            "key.ravyn.clickgui",
            InputUtil.Type.KEYSYM,
            GLFW.GLFW_KEY_RIGHT_SHIFT,
            "category.ravyn.general"
        ));
        
        logger.info("Key bindings registered");
    }
    
    private void registerEventListeners() {
        logger.info("Registering event listeners...");
        
        // Client tick event
        ClientTickEvents.END_CLIENT_TICK.register(client -> {
            try {
                // Handle key presses
                if (clickGuiKey.wasPressed()) {
                    client.setScreen(clickGui);
                }
                
                // Update modules
                moduleManager.onTick();
                
            } catch (Exception e) {
                logger.error("Error in client tick", e);
            }
        });
        
        logger.info("Event listeners registered");
    }
    
    // Getters
    public static RavynClient getInstance() {
        return instance;
    }
    
    public EventManager getEventManager() {
        return eventManager;
    }
    
    public ModuleManager getModuleManager() {
        return moduleManager;
    }
    
    public SettingManager getSettingManager() {
        return settingManager;
    }
    
    public ClickGui getClickGui() {
        return clickGui;
    }

    public CommandManager getCommandManager() {
        return commandManager;
    }

    public FriendManager getFriendManager() {
        return friendManager;
    }

    public ConfigManager getConfigManager() {
        return configManager;
    }

    public KeyBinding getClickGuiKey() {
        return clickGuiKey;
    }
}
