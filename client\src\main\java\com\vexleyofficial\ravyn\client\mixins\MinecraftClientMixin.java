package com.vexleyofficial.ravyn.client.mixins;

import com.vexleyofficial.ravyn.client.RavynClient;
import com.vexleyofficial.ravyn.client.event.events.TickEvent;
import com.vexleyofficial.ravyn.client.module.modules.utility.Timer;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.render.RenderTickCounter;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

/**
 * Mixin for MinecraftClient to hook into client events
 */
@Mixin(MinecraftClient.class)
public class MinecraftClientMixin {
    
    @Shadow private RenderTickCounter renderTickCounter;
    
    @Inject(method = "tick", at = @At("HEAD"))
    private void onTickPre(CallbackInfo ci) {
        if (RavynClient.getInstance() != null) {
            RavynClient.getInstance().getEventManager().post(new TickEvent.Pre());
        }
    }
    
    @Inject(method = "tick", at = @At("TAIL"))
    private void onTickPost(CallbackInfo ci) {
        if (RavynClient.getInstance() != null) {
            RavynClient.getInstance().getEventManager().post(new TickEvent.Post());
        }
    }
    
    @Inject(method = "render", at = @At("HEAD"))
    private void onRenderPre(boolean tick, CallbackInfo ci) {
        // Apply timer speed
        if (RavynClient.getInstance() != null) {
            Timer timer = RavynClient.getInstance().getModuleManager().getModule(Timer.class);
            if (timer != null && timer.isEnabled()) {
                renderTickCounter.tickTime = 1000.0f / (20.0f * timer.getSpeed());
            }
        }
    }
}
